/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2023 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
!function webpackUniversalModuleDefinition(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=e.pdfjsWorker=t():"function"==typeof define&&define.amd?define("pdfjs-dist/build/pdf.worker",[],(()=>e.pdfjsWorker=t())):"object"==typeof exports?exports["pdfjs-dist/build/pdf.worker"]=e.pdfjsWorker=t():e["pdfjs-dist/build/pdf.worker"]=e.pdfjsWorker=t()}(globalThis,(()=>(()=>{"use strict";var e=[,(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0});t.WorkerTask=t.WorkerMessageHandler=void 0;var r=a(2),n=a(3),i=a(4),s=a(6),o=a(10),c=a(68),l=a(73),h=a(104),u=a(105),d=a(72);class WorkerTask{constructor(e){this.name=e;this.terminated=!1;this._capability=new r.PromiseCapability}get finished(){return this._capability.promise}finish(){this._capability.resolve()}terminate(){this.terminated=!0}ensureNotTerminated(){if(this.terminated)throw new Error("Worker task was terminated")}}t.WorkerTask=WorkerTask;class WorkerMessageHandler{static setup(e,t){let a=!1;e.on("test",(function(t){if(!a){a=!0;e.send("test",t instanceof Uint8Array)}}));e.on("configure",(function(e){(0,r.setVerbosityLevel)(e.verbosity)}));e.on("GetDocRequest",(function(e){return WorkerMessageHandler.createDocumentHandler(e,t)}))}static createDocumentHandler(e,t){let a,f=!1,g=null;const p=new Set,m=(0,r.getVerbosityLevel)(),{docId:b,apiVersion:y}=e,w="3.11.174";if(y!==w)throw new Error(`The API version "${y}" does not match the Worker version "${w}".`);const S=[];for(const e in[])S.push(e);if(S.length)throw new Error("The `Array.prototype` contains unexpected enumerable properties: "+S.join(", ")+"; thus breaking e.g. `for...in` iteration of `Array`s.");const x=b+"_worker";let C=new h.MessageHandler(x,b,t);function ensureNotTerminated(){if(f)throw new Error("Worker was terminated")}function startWorkerTask(e){p.add(e)}function finishWorkerTask(e){e.finish();p.delete(e)}async function loadDocument(e){await a.ensureDoc("checkHeader");await a.ensureDoc("parseStartXRef");await a.ensureDoc("parse",[e]);await a.ensureDoc("checkFirstPage",[e]);await a.ensureDoc("checkLastPage",[e]);const t=await a.ensureDoc("isPureXfa");if(t){const e=new WorkerTask("loadXfaFonts");startWorkerTask(e);await Promise.all([a.loadXfaFonts(C,e).catch((e=>{})).then((()=>finishWorkerTask(e))),a.loadXfaImages()])}const[r,n]=await Promise.all([a.ensureDoc("numPages"),a.ensureDoc("fingerprints")]);return{numPages:r,fingerprints:n,htmlForXfa:t?await a.ensureDoc("htmlForXfa"):null}}function getPdfManager({data:e,password:t,disableAutoFetch:a,rangeChunkSize:i,length:o,docBaseUrl:c,enableXfa:l,evaluatorOptions:h}){const d={source:null,disableAutoFetch:a,docBaseUrl:c,docId:b,enableXfa:l,evaluatorOptions:h,handler:C,length:o,password:t,rangeChunkSize:i},f=new r.PromiseCapability;let p;if(e){try{d.source=e;p=new s.LocalPdfManager(d);f.resolve(p)}catch(e){f.reject(e)}return f.promise}let m,y=[];try{m=new u.PDFWorkerStream(C)}catch(e){f.reject(e);return f.promise}const w=m.getFullReader();w.headersReady.then((function(){if(w.isRangeSupported){d.source=m;d.length=w.contentLength;d.disableAutoFetch||=w.isStreamingSupported;p=new s.NetworkPdfManager(d);for(const e of y)p.sendProgressiveData(e);y=[];f.resolve(p);g=null}})).catch((function(e){f.reject(e);g=null}));let S=0;new Promise((function(e,t){const readChunk=function({value:e,done:a}){try{ensureNotTerminated();if(a){p||function(){const e=(0,n.arrayBuffersToBytes)(y);o&&e.length!==o&&(0,r.warn)("reported HTTP length is different from actual");try{d.source=e;p=new s.LocalPdfManager(d);f.resolve(p)}catch(e){f.reject(e)}y=[]}();g=null;return}S+=e.byteLength;w.isStreamingSupported||C.send("DocProgress",{loaded:S,total:Math.max(S,w.contentLength||0)});p?p.sendProgressiveData(e):y.push(e);w.read().then(readChunk,t)}catch(e){t(e)}};w.read().then(readChunk,t)})).catch((function(e){f.reject(e);g=null}));g=function(e){m.cancelAllRequests(e)};return f.promise}C.on("GetPage",(function(e){return a.getPage(e.pageIndex).then((function(e){return Promise.all([a.ensure(e,"rotate"),a.ensure(e,"ref"),a.ensure(e,"userUnit"),a.ensure(e,"view")]).then((function([e,t,a,r]){return{rotate:e,ref:t,userUnit:a,view:r}}))}))}));C.on("GetPageIndex",(function(e){const t=i.Ref.get(e.num,e.gen);return a.ensureCatalog("getPageIndex",[t])}));C.on("GetDestinations",(function(e){return a.ensureCatalog("destinations")}));C.on("GetDestination",(function(e){return a.ensureCatalog("getDestination",[e.id])}));C.on("GetPageLabels",(function(e){return a.ensureCatalog("pageLabels")}));C.on("GetPageLayout",(function(e){return a.ensureCatalog("pageLayout")}));C.on("GetPageMode",(function(e){return a.ensureCatalog("pageMode")}));C.on("GetViewerPreferences",(function(e){return a.ensureCatalog("viewerPreferences")}));C.on("GetOpenAction",(function(e){return a.ensureCatalog("openAction")}));C.on("GetAttachments",(function(e){return a.ensureCatalog("attachments")}));C.on("GetDocJSActions",(function(e){return a.ensureCatalog("jsActions")}));C.on("GetPageJSActions",(function({pageIndex:e}){return a.getPage(e).then((function(e){return a.ensure(e,"jsActions")}))}));C.on("GetOutline",(function(e){return a.ensureCatalog("documentOutline")}));C.on("GetOptionalContentConfig",(function(e){return a.ensureCatalog("optionalContentConfig")}));C.on("GetPermissions",(function(e){return a.ensureCatalog("permissions")}));C.on("GetMetadata",(function(e){return Promise.all([a.ensureDoc("documentInfo"),a.ensureCatalog("metadata")])}));C.on("GetMarkInfo",(function(e){return a.ensureCatalog("markInfo")}));C.on("GetData",(function(e){return a.requestLoadedStream().then((function(e){return e.bytes}))}));C.on("GetAnnotations",(function({pageIndex:e,intent:t}){return a.getPage(e).then((function(a){const r=new WorkerTask(`GetAnnotations: page ${e}`);startWorkerTask(r);return a.getAnnotationsData(C,r,t).then((e=>{finishWorkerTask(r);return e}),(e=>{finishWorkerTask(r);throw e}))}))}));C.on("GetFieldObjects",(function(e){return a.ensureDoc("fieldObjects")}));C.on("HasJSActions",(function(e){return a.ensureDoc("hasJSActions")}));C.on("GetCalculationOrderIds",(function(e){return a.ensureDoc("calculationOrderIds")}));C.on("SaveDocument",(async function({isPureXfa:e,numPages:t,annotationStorage:s,filename:c}){const h=[a.requestLoadedStream(),a.ensureCatalog("acroForm"),a.ensureCatalog("acroFormRef"),a.ensureDoc("startXRef"),a.ensureDoc("xref"),a.ensureDoc("linearization"),a.ensureCatalog("structTreeRoot")],u=[],f=e?null:(0,n.getNewAnnotationsMap)(s),[g,p,m,b,y,w,S]=await Promise.all(h),x=y.trailer.getRaw("Root")||null;let k;if(f){S?await S.canUpdateStructTree({pdfManager:a,newAnnotationsByPage:f})&&(k=S):await d.StructTreeRoot.canCreateStructureTree({catalogRef:x,pdfManager:a,newAnnotationsByPage:f})&&(k=null);const e=o.AnnotationFactory.generateImages(s.values(),y,a.evaluatorOptions.isOffscreenCanvasSupported),t=void 0===k?u:[];for(const[r,n]of f)t.push(a.getPage(r).then((t=>{const a=new WorkerTask(`Save (editor): page ${r}`);return t.saveNewAnnotations(C,a,n,e).finally((function(){finishWorkerTask(a)}))})));null===k?u.push(Promise.all(t).then((async e=>{await d.StructTreeRoot.createStructureTree({newAnnotationsByPage:f,xref:y,catalogRef:x,pdfManager:a,newRefs:e});return e}))):k&&u.push(Promise.all(t).then((async e=>{await k.updateStructureTree({newAnnotationsByPage:f,pdfManager:a,newRefs:e});return e})))}if(e)u.push(a.serializeXfaData(s));else for(let e=0;e<t;e++)u.push(a.getPage(e).then((function(t){const a=new WorkerTask(`Save: page ${e}`);return t.save(C,a,s).finally((function(){finishWorkerTask(a)}))})));const v=await Promise.all(u);let F=[],O=null;if(e){O=v[0];if(!O)return g.bytes}else{F=v.flat(2);if(0===F.length)return g.bytes}const T=m&&p instanceof i.Dict&&F.some((e=>e.needAppearances)),M=p instanceof i.Dict&&p.get("XFA")||null;let D=null,E=!1;if(Array.isArray(M)){for(let e=0,t=M.length;e<t;e+=2)if("datasets"===M[e]){D=M[e+1];E=!0}null===D&&(D=y.getNewTemporaryRef())}else M&&(0,r.warn)("Unsupported XFA type.");let N=Object.create(null);if(y.trailer){const e=Object.create(null),t=y.trailer.get("Info")||null;t instanceof i.Dict&&t.forEach(((t,a)=>{"string"==typeof a&&(e[t]=(0,r.stringToPDFString)(a))}));N={rootRef:x,encryptRef:y.trailer.getRaw("Encrypt")||null,newRef:y.getNewTemporaryRef(),infoRef:y.trailer.getRaw("Info")||null,info:e,fileIds:y.trailer.get("ID")||null,startXRef:w?b:y.lastXRefStreamPos??b,filename:c}}return(0,l.incrementalUpdate)({originalData:g.bytes,xrefInfo:N,newRefs:F,xref:y,hasXfa:!!M,xfaDatasetsRef:D,hasXfaDatasetsEntry:E,needAppearances:T,acroFormRef:m,acroForm:p,xfaData:O}).finally((()=>{y.resetNewTemporaryRef()}))}));C.on("GetOperatorList",(function(e,t){const n=e.pageIndex;a.getPage(n).then((function(a){const i=new WorkerTask(`GetOperatorList: page ${n}`);startWorkerTask(i);const s=m>=r.VerbosityLevel.INFOS?Date.now():0;a.getOperatorList({handler:C,sink:t,task:i,intent:e.intent,cacheKey:e.cacheKey,annotationStorage:e.annotationStorage}).then((function(e){finishWorkerTask(i);s&&(0,r.info)(`page=${n+1} - getOperatorList: time=${Date.now()-s}ms, len=${e.length}`);t.close()}),(function(e){finishWorkerTask(i);i.terminated||t.error(e)}))}))}));C.on("GetTextContent",(function(e,t){const{pageIndex:n,includeMarkedContent:i,disableNormalization:s}=e;a.getPage(n).then((function(e){const a=new WorkerTask("GetTextContent: page "+n);startWorkerTask(a);const o=m>=r.VerbosityLevel.INFOS?Date.now():0;e.extractTextContent({handler:C,task:a,sink:t,includeMarkedContent:i,disableNormalization:s}).then((function(){finishWorkerTask(a);o&&(0,r.info)(`page=${n+1} - getTextContent: time=`+(Date.now()-o)+"ms");t.close()}),(function(e){finishWorkerTask(a);a.terminated||t.error(e)}))}))}));C.on("GetStructTree",(function(e){return a.getPage(e.pageIndex).then((function(e){return a.ensure(e,"getStructTree")}))}));C.on("FontFallback",(function(e){return a.fontFallback(e.id,C)}));C.on("Cleanup",(function(e){return a.cleanup(!0)}));C.on("Terminate",(function(e){f=!0;const t=[];if(a){a.terminate(new r.AbortException("Worker was terminated."));const e=a.cleanup();t.push(e);a=null}else(0,c.clearGlobalCaches)();g&&g(new r.AbortException("Worker was terminated."));for(const e of p){t.push(e.finished);e.terminate()}return Promise.all(t).then((function(){C.destroy();C=null}))}));C.on("Ready",(function(t){!function setupDoc(e){function onSuccess(e){ensureNotTerminated();C.send("GetDoc",{pdfInfo:e})}function onFailure(e){ensureNotTerminated();if(e instanceof r.PasswordException){const t=new WorkerTask(`PasswordException: response ${e.code}`);startWorkerTask(t);C.sendWithPromise("PasswordRequest",e).then((function({password:e}){finishWorkerTask(t);a.updatePassword(e);pdfManagerReady()})).catch((function(){finishWorkerTask(t);C.send("DocException",e)}))}else e instanceof r.InvalidPDFException||e instanceof r.MissingPDFException||e instanceof r.UnexpectedResponseException||e instanceof r.UnknownErrorException?C.send("DocException",e):C.send("DocException",new r.UnknownErrorException(e.message,e.toString()))}function pdfManagerReady(){ensureNotTerminated();loadDocument(!1).then(onSuccess,(function(e){ensureNotTerminated();e instanceof n.XRefParseException?a.requestLoadedStream().then((function(){ensureNotTerminated();loadDocument(!0).then(onSuccess,onFailure)})):onFailure(e)}))}ensureNotTerminated();getPdfManager(e).then((function(e){if(f){e.terminate(new r.AbortException("Worker was terminated."));throw new Error("Worker was terminated")}a=e;a.requestLoadedStream(!0).then((e=>{C.send("DataLoaded",{length:e.bytes.byteLength})}))})).then(pdfManagerReady,onFailure)}(e);e=null}));return x}static initializeFromPort(e){const t=new h.MessageHandler("worker","main",e);WorkerMessageHandler.setup(t,e);t.send("ready",null)}}t.WorkerMessageHandler=WorkerMessageHandler;"undefined"==typeof window&&!r.isNodeJS&&"undefined"!=typeof self&&function isMessagePort(e){return"function"==typeof e.postMessage&&"onmessage"in e}(self)&&WorkerMessageHandler.initializeFromPort(self)},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.VerbosityLevel=t.Util=t.UnknownErrorException=t.UnexpectedResponseException=t.TextRenderingMode=t.RenderingIntentFlag=t.PromiseCapability=t.PermissionFlag=t.PasswordResponses=t.PasswordException=t.PageActionEventType=t.OPS=t.MissingPDFException=t.MAX_IMAGE_SIZE_TO_CACHE=t.LINE_FACTOR=t.LINE_DESCENT_FACTOR=t.InvalidPDFException=t.ImageKind=t.IDENTITY_MATRIX=t.FormatError=t.FeatureTest=t.FONT_IDENTITY_MATRIX=t.DocumentActionEventType=t.CMapCompressionType=t.BaseException=t.BASELINE_FACTOR=t.AnnotationType=t.AnnotationReplyType=t.AnnotationPrefix=t.AnnotationMode=t.AnnotationFlag=t.AnnotationFieldFlag=t.AnnotationEditorType=t.AnnotationEditorPrefix=t.AnnotationEditorParamsType=t.AnnotationBorderStyleType=t.AnnotationActionEventType=t.AbortException=void 0;t.assert=function assert(e,t){e||unreachable(t)};t.bytesToString=bytesToString;t.createValidAbsoluteUrl=function createValidAbsoluteUrl(e,t=null,a=null){if(!e)return null;try{if(a&&"string"==typeof e){if(a.addDefaultProtocol&&e.startsWith("www.")){const t=e.match(/\./g);t?.length>=2&&(e=`http://${e}`)}if(a.tryConvertEncoding)try{e=stringToUTF8String(e)}catch{}}const r=t?new URL(e,t):new URL(e);if(function _isValidProtocol(e){switch(e?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(r))return r}catch{}return null};t.getModificationDate=function getModificationDate(e=new Date){return[e.getUTCFullYear().toString(),(e.getUTCMonth()+1).toString().padStart(2,"0"),e.getUTCDate().toString().padStart(2,"0"),e.getUTCHours().toString().padStart(2,"0"),e.getUTCMinutes().toString().padStart(2,"0"),e.getUTCSeconds().toString().padStart(2,"0")].join("")};t.getUuid=function getUuid(){if("undefined"!=typeof crypto&&"function"==typeof crypto?.randomUUID)return crypto.randomUUID();const e=new Uint8Array(32);if("undefined"!=typeof crypto&&"function"==typeof crypto?.getRandomValues)crypto.getRandomValues(e);else for(let t=0;t<32;t++)e[t]=Math.floor(255*Math.random());return bytesToString(e)};t.getVerbosityLevel=function getVerbosityLevel(){return n};t.info=function info(e){n>=r.INFOS&&console.log(`Info: ${e}`)};t.isArrayBuffer=function isArrayBuffer(e){return"object"==typeof e&&void 0!==e?.byteLength};t.isArrayEqual=function isArrayEqual(e,t){if(e.length!==t.length)return!1;for(let a=0,r=e.length;a<r;a++)if(e[a]!==t[a])return!1;return!0};t.isNodeJS=void 0;t.normalizeUnicode=function normalizeUnicode(e){if(!c){c=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu;l=new Map([["ﬅ","ſt"]])}return e.replaceAll(c,((e,t,a)=>t?t.normalize("NFKC"):l.get(a)))};t.objectFromMap=function objectFromMap(e){const t=Object.create(null);for(const[a,r]of e)t[a]=r;return t};t.objectSize=function objectSize(e){return Object.keys(e).length};t.setVerbosityLevel=function setVerbosityLevel(e){Number.isInteger(e)&&(n=e)};t.shadow=shadow;t.string32=function string32(e){return String.fromCharCode(e>>24&255,e>>16&255,e>>8&255,255&e)};t.stringToBytes=stringToBytes;t.stringToPDFString=function stringToPDFString(e){if(e[0]>="ï"){let t;"þ"===e[0]&&"ÿ"===e[1]?t="utf-16be":"ÿ"===e[0]&&"þ"===e[1]?t="utf-16le":"ï"===e[0]&&"»"===e[1]&&"¿"===e[2]&&(t="utf-8");if(t)try{const a=new TextDecoder(t,{fatal:!0}),r=stringToBytes(e);return a.decode(r)}catch(e){warn(`stringToPDFString: "${e}".`)}}const t=[];for(let a=0,r=e.length;a<r;a++){const r=o[e.charCodeAt(a)];t.push(r?String.fromCharCode(r):e.charAt(a))}return t.join("")};t.stringToUTF8String=stringToUTF8String;t.unreachable=unreachable;t.utf8StringToString=function utf8StringToString(e){return unescape(encodeURIComponent(e))};t.warn=warn;const a=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type);t.isNodeJS=a;t.IDENTITY_MATRIX=[1,0,0,1,0,0];t.FONT_IDENTITY_MATRIX=[.001,0,0,.001,0,0];t.MAX_IMAGE_SIZE_TO_CACHE=1e7;t.LINE_FACTOR=1.35;t.LINE_DESCENT_FACTOR=.35;t.BASELINE_FACTOR=.25925925925925924;t.RenderingIntentFlag={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256};t.AnnotationMode={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3};t.AnnotationEditorPrefix="pdfjs_internal_editor_";t.AnnotationEditorType={DISABLE:-1,NONE:0,FREETEXT:3,STAMP:13,INK:15};t.AnnotationEditorParamsType={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23};t.PermissionFlag={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};t.TextRenderingMode={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};t.ImageKind={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};t.AnnotationType={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};t.AnnotationReplyType={GROUP:"Group",REPLY:"R"};t.AnnotationFlag={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};t.AnnotationFieldFlag={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};t.AnnotationBorderStyleType={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};t.AnnotationActionEventType={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"};t.DocumentActionEventType={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"};t.PageActionEventType={O:"PageOpen",C:"PageClose"};const r={ERRORS:0,WARNINGS:1,INFOS:5};t.VerbosityLevel=r;t.CMapCompressionType={NONE:0,BINARY:1};t.OPS={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};t.PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let n=r.WARNINGS;function warn(e){n>=r.WARNINGS&&console.log(`Warning: ${e}`)}function unreachable(e){throw new Error(e)}function shadow(e,t,a,r=!1){Object.defineProperty(e,t,{value:a,enumerable:!r,configurable:!0,writable:!1});return a}const i=function BaseExceptionClosure(){function BaseException(e,t){this.constructor===BaseException&&unreachable("Cannot initialize BaseException.");this.message=e;this.name=t}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();t.BaseException=i;t.PasswordException=class PasswordException extends i{constructor(e,t){super(e,"PasswordException");this.code=t}};t.UnknownErrorException=class UnknownErrorException extends i{constructor(e,t){super(e,"UnknownErrorException");this.details=t}};t.InvalidPDFException=class InvalidPDFException extends i{constructor(e){super(e,"InvalidPDFException")}};t.MissingPDFException=class MissingPDFException extends i{constructor(e){super(e,"MissingPDFException")}};t.UnexpectedResponseException=class UnexpectedResponseException extends i{constructor(e,t){super(e,"UnexpectedResponseException");this.status=t}};t.FormatError=class FormatError extends i{constructor(e){super(e,"FormatError")}};t.AbortException=class AbortException extends i{constructor(e){super(e,"AbortException")}};function bytesToString(e){"object"==typeof e&&void 0!==e?.length||unreachable("Invalid argument for bytesToString");const t=e.length,a=8192;if(t<a)return String.fromCharCode.apply(null,e);const r=[];for(let n=0;n<t;n+=a){const i=Math.min(n+a,t),s=e.subarray(n,i);r.push(String.fromCharCode.apply(null,s))}return r.join("")}function stringToBytes(e){"string"!=typeof e&&unreachable("Invalid argument for stringToBytes");const t=e.length,a=new Uint8Array(t);for(let r=0;r<t;++r)a[r]=255&e.charCodeAt(r);return a}t.FeatureTest=class FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const e=new Uint8Array(4);e[0]=1;return 1===new Uint32Array(e.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get platform(){return"undefined"==typeof navigator?shadow(this,"platform",{isWin:!1,isMac:!1}):shadow(this,"platform",{isWin:navigator.platform.includes("Win"),isMac:navigator.platform.includes("Mac")})}static get isCSSRoundSupported(){return shadow(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}};const s=[...Array(256).keys()].map((e=>e.toString(16).padStart(2,"0")));t.Util=class Util{static makeHexColor(e,t,a){return`#${s[e]}${s[t]}${s[a]}`}static scaleMinMax(e,t){let a;if(e[0]){if(e[0]<0){a=t[0];t[0]=t[1];t[1]=a}t[0]*=e[0];t[1]*=e[0];if(e[3]<0){a=t[2];t[2]=t[3];t[3]=a}t[2]*=e[3];t[3]*=e[3]}else{a=t[0];t[0]=t[2];t[2]=a;a=t[1];t[1]=t[3];t[3]=a;if(e[1]<0){a=t[2];t[2]=t[3];t[3]=a}t[2]*=e[1];t[3]*=e[1];if(e[2]<0){a=t[0];t[0]=t[1];t[1]=a}t[0]*=e[2];t[1]*=e[2]}t[0]+=e[4];t[1]+=e[4];t[2]+=e[5];t[3]+=e[5]}static transform(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]}static applyTransform(e,t){return[e[0]*t[0]+e[1]*t[2]+t[4],e[0]*t[1]+e[1]*t[3]+t[5]]}static applyInverseTransform(e,t){const a=t[0]*t[3]-t[1]*t[2];return[(e[0]*t[3]-e[1]*t[2]+t[2]*t[5]-t[4]*t[3])/a,(-e[0]*t[1]+e[1]*t[0]+t[4]*t[1]-t[5]*t[0])/a]}static getAxialAlignedBoundingBox(e,t){const a=this.applyTransform(e,t),r=this.applyTransform(e.slice(2,4),t),n=this.applyTransform([e[0],e[3]],t),i=this.applyTransform([e[2],e[1]],t);return[Math.min(a[0],r[0],n[0],i[0]),Math.min(a[1],r[1],n[1],i[1]),Math.max(a[0],r[0],n[0],i[0]),Math.max(a[1],r[1],n[1],i[1])]}static inverseTransform(e){const t=e[0]*e[3]-e[1]*e[2];return[e[3]/t,-e[1]/t,-e[2]/t,e[0]/t,(e[2]*e[5]-e[4]*e[3])/t,(e[4]*e[1]-e[5]*e[0])/t]}static singularValueDecompose2dScale(e){const t=[e[0],e[2],e[1],e[3]],a=e[0]*t[0]+e[1]*t[2],r=e[0]*t[1]+e[1]*t[3],n=e[2]*t[0]+e[3]*t[2],i=e[2]*t[1]+e[3]*t[3],s=(a+i)/2,o=Math.sqrt((a+i)**2-4*(a*i-n*r))/2,c=s+o||1,l=s-o||1;return[Math.sqrt(c),Math.sqrt(l)]}static normalizeRect(e){const t=e.slice(0);if(e[0]>e[2]){t[0]=e[2];t[2]=e[0]}if(e[1]>e[3]){t[1]=e[3];t[3]=e[1]}return t}static intersect(e,t){const a=Math.max(Math.min(e[0],e[2]),Math.min(t[0],t[2])),r=Math.min(Math.max(e[0],e[2]),Math.max(t[0],t[2]));if(a>r)return null;const n=Math.max(Math.min(e[1],e[3]),Math.min(t[1],t[3])),i=Math.min(Math.max(e[1],e[3]),Math.max(t[1],t[3]));return n>i?null:[a,n,r,i]}static bezierBoundingBox(e,t,a,r,n,i,s,o){const c=[],l=[[],[]];let h,u,d,f,g,p,m,b;for(let l=0;l<2;++l){if(0===l){u=6*e-12*a+6*n;h=-3*e+9*a-9*n+3*s;d=3*a-3*e}else{u=6*t-12*r+6*i;h=-3*t+9*r-9*i+3*o;d=3*r-3*t}if(Math.abs(h)<1e-12){if(Math.abs(u)<1e-12)continue;f=-d/u;0<f&&f<1&&c.push(f)}else{m=u*u-4*d*h;b=Math.sqrt(m);if(!(m<0)){g=(-u+b)/(2*h);0<g&&g<1&&c.push(g);p=(-u-b)/(2*h);0<p&&p<1&&c.push(p)}}}let y,w=c.length;const S=w;for(;w--;){f=c[w];y=1-f;l[0][w]=y*y*y*e+3*y*y*f*a+3*y*f*f*n+f*f*f*s;l[1][w]=y*y*y*t+3*y*y*f*r+3*y*f*f*i+f*f*f*o}l[0][S]=e;l[1][S]=t;l[0][S+1]=s;l[1][S+1]=o;l[0].length=l[1].length=S+2;return[Math.min(...l[0]),Math.min(...l[1]),Math.max(...l[0]),Math.max(...l[1])]}};const o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function stringToUTF8String(e){return decodeURIComponent(escape(e))}t.PromiseCapability=class PromiseCapability{#e=!1;constructor(){this.promise=new Promise(((e,t)=>{this.resolve=t=>{this.#e=!0;e(t)};this.reject=e=>{this.#e=!0;t(e)}}))}get settled(){return this.#e}};let c=null,l=null;t.AnnotationPrefix="pdfjs_internal_id_"},(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0});t.XRefParseException=t.XRefEntryException=t.ParserEOFException=t.PDF_VERSION_REGEXP=t.MissingDataException=void 0;t.arrayBuffersToBytes=function arrayBuffersToBytes(e){const t=e.length;if(0===t)return new Uint8Array(0);if(1===t)return new Uint8Array(e[0]);let a=0;for(let r=0;r<t;r++)a+=e[r].byteLength;const r=new Uint8Array(a);let n=0;for(let a=0;a<t;a++){const t=new Uint8Array(e[a]);r.set(t,n);n+=t.byteLength}return r};t.collectActions=function collectActions(e,t,a){const i=Object.create(null),s=getInheritableProperty({dict:t,key:"AA",stopWhenFound:!1});if(s)for(let t=s.length-1;t>=0;t--){const r=s[t];if(r instanceof n.Dict)for(const t of r.getKeys()){const s=a[t];if(!s)continue;const o=[];_collectJS(r.getRaw(t),e,o,new n.RefSet);o.length>0&&(i[s]=o)}}if(t.has("A")){const a=[];_collectJS(t.get("A"),e,a,new n.RefSet);a.length>0&&(i.Action=a)}return(0,r.objectSize)(i)>0?i:null};t.encodeToXmlString=function encodeToXmlString(e){const t=[];let a=0;for(let r=0,n=e.length;r<n;r++){const n=e.codePointAt(r);if(32<=n&&n<=126){const i=o[n];if(i){a<r&&t.push(e.substring(a,r));t.push(i);a=r+1}}else{a<r&&t.push(e.substring(a,r));t.push(`&#x${n.toString(16).toUpperCase()};`);n>55295&&(n<57344||n>65533)&&r++;a=r+1}}if(0===t.length)return e;a<e.length&&t.push(e.substring(a,e.length));return t.join("")};t.escapePDFName=function escapePDFName(e){const t=[];let a=0;for(let r=0,n=e.length;r<n;r++){const n=e.charCodeAt(r);if(n<33||n>126||35===n||40===n||41===n||60===n||62===n||91===n||93===n||123===n||125===n||47===n||37===n){a<r&&t.push(e.substring(a,r));t.push(`#${n.toString(16)}`);a=r+1}}if(0===t.length)return e;a<e.length&&t.push(e.substring(a,e.length));return t.join("")};t.escapeString=function escapeString(e){return e.replaceAll(/([()\\\n\r])/g,(e=>"\n"===e?"\\n":"\r"===e?"\\r":`\\${e}`))};t.getInheritableProperty=getInheritableProperty;t.getLookupTableFactory=function getLookupTableFactory(e){let t;return function(){if(e){t=Object.create(null);e(t);e=null}return t}};t.getNewAnnotationsMap=function getNewAnnotationsMap(e){if(!e)return null;const t=new Map;for(const[a,n]of e){if(!a.startsWith(r.AnnotationEditorPrefix))continue;let e=t.get(n.pageIndex);if(!e){e=[];t.set(n.pageIndex,e)}e.push(n)}return t.size>0?t:null};t.getRotationMatrix=function getRotationMatrix(e,t,a){switch(e){case 90:return[0,1,-1,0,t,0];case 180:return[-1,0,0,-1,t,a];case 270:return[0,-1,1,0,0,a];default:throw new Error("Invalid rotation")}};t.isAscii=function isAscii(e){return/^[\x00-\x7F]*$/.test(e)};t.isWhiteSpace=function isWhiteSpace(e){return 32===e||9===e||13===e||10===e};t.log2=function log2(e){if(e<=0)return 0;return Math.ceil(Math.log2(e))};t.numberToString=function numberToString(e){if(Number.isInteger(e))return e.toString();const t=Math.round(100*e);if(t%100==0)return(t/100).toString();if(t%10==0)return e.toFixed(1);return e.toFixed(2)};t.parseXFAPath=function parseXFAPath(e){const t=/(.+)\[(\d+)\]$/;return e.split(".").map((e=>{const a=e.match(t);return a?{name:a[1],pos:parseInt(a[2],10)}:{name:e,pos:0}}))};t.readInt8=function readInt8(e,t){return e[t]<<24>>24};t.readUint16=function readUint16(e,t){return e[t]<<8|e[t+1]};t.readUint32=function readUint32(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0};t.recoverJsURL=function recoverJsURL(e){const t=new RegExp("^\\s*("+["app.launchURL","window.open","xfa.host.gotoURL"].join("|").replaceAll(".","\\.")+")\\((?:'|\")([^'\"]*)(?:'|\")(?:,\\s*(\\w+)\\)|\\))","i").exec(e);if(t?.[2]){const e=t[2];let a=!1;"true"===t[3]&&"app.launchURL"===t[1]&&(a=!0);return{url:e,newWindow:a}}return null};t.stringToUTF16HexString=function stringToUTF16HexString(e){const t=[];for(let a=0,r=e.length;a<r;a++){const r=e.charCodeAt(a);t.push((r>>8&255).toString(16).padStart(2,"0"),(255&r).toString(16).padStart(2,"0"))}return t.join("")};t.stringToUTF16String=function stringToUTF16String(e,t=!1){const a=[];t&&a.push("þÿ");for(let t=0,r=e.length;t<r;t++){const r=e.charCodeAt(t);a.push(String.fromCharCode(r>>8&255),String.fromCharCode(255&r))}return a.join("")};t.toRomanNumerals=function toRomanNumerals(e,t=!1){(0,r.assert)(Number.isInteger(e)&&e>0,"The number should be a positive integer.");const a=[];let n;for(;e>=1e3;){e-=1e3;a.push("M")}n=e/100|0;e%=100;a.push(s[n]);n=e/10|0;e%=10;a.push(s[10+n]);a.push(s[20+e]);const i=a.join("");return t?i.toLowerCase():i};t.validateCSSFont=function validateCSSFont(e){const t=new Set(["100","200","300","400","500","600","700","800","900","1000","normal","bold","bolder","lighter"]),{fontFamily:a,fontWeight:r,italicAngle:n}=e;if(!validateFontName(a,!0))return!1;const i=r?r.toString():"";e.fontWeight=t.has(i)?i:"400";const s=parseFloat(n);e.italicAngle=isNaN(s)||s<-90||s>90?"14":n.toString();return!0};t.validateFontName=validateFontName;var r=a(2),n=a(4),i=a(5);t.PDF_VERSION_REGEXP=/^[1-9]\.\d$/;class MissingDataException extends r.BaseException{constructor(e,t){super(`Missing data [${e}, ${t})`,"MissingDataException");this.begin=e;this.end=t}}t.MissingDataException=MissingDataException;class ParserEOFException extends r.BaseException{constructor(e){super(e,"ParserEOFException")}}t.ParserEOFException=ParserEOFException;class XRefEntryException extends r.BaseException{constructor(e){super(e,"XRefEntryException")}}t.XRefEntryException=XRefEntryException;class XRefParseException extends r.BaseException{constructor(e){super(e,"XRefParseException")}}t.XRefParseException=XRefParseException;function getInheritableProperty({dict:e,key:t,getArray:a=!1,stopWhenFound:r=!0}){let i;const s=new n.RefSet;for(;e instanceof n.Dict&&(!e.objId||!s.has(e.objId));){e.objId&&s.put(e.objId);const n=a?e.getArray(t):e.get(t);if(void 0!==n){if(r)return n;(i||=[]).push(n)}e=e.get("Parent")}return i}const s=["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM","","X","XX","XXX","XL","L","LX","LXX","LXXX","XC","","I","II","III","IV","V","VI","VII","VIII","IX"];function _collectJS(e,t,a,s){if(!e)return;let o=null;if(e instanceof n.Ref){if(s.has(e))return;o=e;s.put(o);e=t.fetch(e)}if(Array.isArray(e))for(const r of e)_collectJS(r,t,a,s);else if(e instanceof n.Dict){if((0,n.isName)(e.get("S"),"JavaScript")){const t=e.get("JS");let n;t instanceof i.BaseStream?n=t.getString():"string"==typeof t&&(n=t);n&&=(0,r.stringToPDFString)(n).replaceAll("\0","");n&&a.push(n)}_collectJS(e.getRaw("Next"),t,a,s)}o&&s.remove(o)}const o={60:"&lt;",62:"&gt;",38:"&amp;",34:"&quot;",39:"&apos;"};function validateFontName(e,t=!1){const a=/^("|').*("|')$/.exec(e);if(a&&a[1]===a[2]){if(new RegExp(`[^\\\\]${a[1]}`).test(e.slice(1,-1))){t&&(0,r.warn)(`FontFamily contains unescaped ${a[1]}: ${e}.`);return!1}}else for(const a of e.split(/[ \t]+/))if(/^(\d|(-(\d|-)))/.test(a)||!/^[\w-\\]+$/.test(a)){t&&(0,r.warn)(`FontFamily contains invalid <custom-ident>: ${e}.`);return!1}return!0}},(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0});t.RefSetCache=t.RefSet=t.Ref=t.Name=t.EOF=t.Dict=t.Cmd=t.CIRCULAR_REF=void 0;t.clearPrimitiveCaches=function clearPrimitiveCaches(){s=Object.create(null);o=Object.create(null);c=Object.create(null)};t.isCmd=function isCmd(e,t){return e instanceof Cmd&&(void 0===t||e.cmd===t)};t.isDict=function isDict(e,t){return e instanceof Dict&&(void 0===t||isName(e.get("Type"),t))};t.isName=isName;t.isRefsEqual=function isRefsEqual(e,t){return e.num===t.num&&e.gen===t.gen};var r=a(2);const n=Symbol("CIRCULAR_REF");t.CIRCULAR_REF=n;const i=Symbol("EOF");t.EOF=i;let s=Object.create(null),o=Object.create(null),c=Object.create(null);class Name{constructor(e){this.name=e}static get(e){return o[e]||=new Name(e)}}t.Name=Name;class Cmd{constructor(e){this.cmd=e}static get(e){return s[e]||=new Cmd(e)}}t.Cmd=Cmd;const l=function nonSerializableClosure(){return l};class Dict{constructor(e=null){this._map=Object.create(null);this.xref=e;this.objId=null;this.suppressEncryption=!1;this.__nonSerializable__=l}assignXref(e){this.xref=e}get size(){return Object.keys(this._map).length}get(e,t,a){let r=this._map[e];if(void 0===r&&void 0!==t){r=this._map[t];void 0===r&&void 0!==a&&(r=this._map[a])}return r instanceof Ref&&this.xref?this.xref.fetch(r,this.suppressEncryption):r}async getAsync(e,t,a){let r=this._map[e];if(void 0===r&&void 0!==t){r=this._map[t];void 0===r&&void 0!==a&&(r=this._map[a])}return r instanceof Ref&&this.xref?this.xref.fetchAsync(r,this.suppressEncryption):r}getArray(e,t,a){let r=this._map[e];if(void 0===r&&void 0!==t){r=this._map[t];void 0===r&&void 0!==a&&(r=this._map[a])}r instanceof Ref&&this.xref&&(r=this.xref.fetch(r,this.suppressEncryption));if(Array.isArray(r)){r=r.slice();for(let e=0,t=r.length;e<t;e++)r[e]instanceof Ref&&this.xref&&(r[e]=this.xref.fetch(r[e],this.suppressEncryption))}return r}getRaw(e){return this._map[e]}getKeys(){return Object.keys(this._map)}getRawValues(){return Object.values(this._map)}set(e,t){this._map[e]=t}has(e){return void 0!==this._map[e]}forEach(e){for(const t in this._map)e(t,this.get(t))}static get empty(){const e=new Dict(null);e.set=(e,t)=>{(0,r.unreachable)("Should not call `set` on the empty dictionary.")};return(0,r.shadow)(this,"empty",e)}static merge({xref:e,dictArray:t,mergeSubDicts:a=!1}){const r=new Dict(e),n=new Map;for(const e of t)if(e instanceof Dict)for(const[t,r]of Object.entries(e._map)){let e=n.get(t);if(void 0===e){e=[];n.set(t,e)}else if(!(a&&r instanceof Dict))continue;e.push(r)}for(const[t,a]of n){if(1===a.length||!(a[0]instanceof Dict)){r._map[t]=a[0];continue}const n=new Dict(e);for(const e of a)for(const[t,a]of Object.entries(e._map))void 0===n._map[t]&&(n._map[t]=a);n.size>0&&(r._map[t]=n)}n.clear();return r.size>0?r:Dict.empty}clone(){const e=new Dict(this.xref);for(const t of this.getKeys())e.set(t,this.getRaw(t));return e}}t.Dict=Dict;class Ref{constructor(e,t){this.num=e;this.gen=t}toString(){return 0===this.gen?`${this.num}R`:`${this.num}R${this.gen}`}static fromString(e){const t=c[e];if(t)return t;const a=/^(\d+)R(\d*)$/.exec(e);return a&&"0"!==a[1]?c[e]=new Ref(parseInt(a[1]),a[2]?parseInt(a[2]):0):null}static get(e,t){const a=0===t?`${e}R`:`${e}R${t}`;return c[a]||=new Ref(e,t)}}t.Ref=Ref;class RefSet{constructor(e=null){this._set=new Set(e?._set)}has(e){return this._set.has(e.toString())}put(e){this._set.add(e.toString())}remove(e){this._set.delete(e.toString())}[Symbol.iterator](){return this._set.values()}clear(){this._set.clear()}}t.RefSet=RefSet;class RefSetCache{constructor(){this._map=new Map}get size(){return this._map.size}get(e){return this._map.get(e.toString())}has(e){return this._map.has(e.toString())}put(e,t){this._map.set(e.toString(),t)}putAlias(e,t){this._map.set(e.toString(),this.get(t))}[Symbol.iterator](){return this._map.values()}clear(){this._map.clear()}}t.RefSetCache=RefSetCache;function isName(e,t){return e instanceof Name&&(void 0===t||e.name===t)}},(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0});t.BaseStream=void 0;var r=a(2);class BaseStream{constructor(){this.constructor===BaseStream&&(0,r.unreachable)("Cannot initialize BaseStream.")}get length(){(0,r.unreachable)("Abstract getter `length` accessed")}get isEmpty(){(0,r.unreachable)("Abstract getter `isEmpty` accessed")}get isDataLoaded(){return(0,r.shadow)(this,"isDataLoaded",!0)}getByte(){(0,r.unreachable)("Abstract method `getByte` called")}getBytes(e){(0,r.unreachable)("Abstract method `getBytes` called")}peekByte(){const e=this.getByte();-1!==e&&this.pos--;return e}peekBytes(e){const t=this.getBytes(e);this.pos-=t.length;return t}getUint16(){const e=this.getByte(),t=this.getByte();return-1===e||-1===t?-1:(e<<8)+t}getInt32(){return(this.getByte()<<24)+(this.getByte()<<16)+(this.getByte()<<8)+this.getByte()}getByteRange(e,t){(0,r.unreachable)("Abstract method `getByteRange` called")}getString(e){return(0,r.bytesToString)(this.getBytes(e))}skip(e){this.pos+=e||1}reset(){(0,r.unreachable)("Abstract method `reset` called")}moveStart(){(0,r.unreachable)("Abstract method `moveStart` called")}makeSubStream(e,t,a=null){(0,r.unreachable)("Abstract method `makeSubStream` called")}getBaseStreams(){return null}}t.BaseStream=BaseStream},(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0});t.NetworkPdfManager=t.LocalPdfManager=void 0;var r=a(2),n=a(7),i=a(3),s=a(9),o=a(8);class BasePdfManager{constructor(e){this.constructor===BasePdfManager&&(0,r.unreachable)("Cannot initialize BasePdfManager.");this._docBaseUrl=function parseDocBaseUrl(e){if(e){const t=(0,r.createValidAbsoluteUrl)(e);if(t)return t.href;(0,r.warn)(`Invalid absolute docBaseUrl: "${e}".`)}return null}(e.docBaseUrl);this._docId=e.docId;this._password=e.password;this.enableXfa=e.enableXfa;e.evaluatorOptions.isOffscreenCanvasSupported&&=r.FeatureTest.isOffscreenCanvasSupported;this.evaluatorOptions=e.evaluatorOptions}get docId(){return this._docId}get password(){return this._password}get docBaseUrl(){return this._docBaseUrl}get catalog(){return this.pdfDocument.catalog}ensureDoc(e,t){return this.ensure(this.pdfDocument,e,t)}ensureXRef(e,t){return this.ensure(this.pdfDocument.xref,e,t)}ensureCatalog(e,t){return this.ensure(this.pdfDocument.catalog,e,t)}getPage(e){return this.pdfDocument.getPage(e)}fontFallback(e,t){return this.pdfDocument.fontFallback(e,t)}loadXfaFonts(e,t){return this.pdfDocument.loadXfaFonts(e,t)}loadXfaImages(){return this.pdfDocument.loadXfaImages()}serializeXfaData(e){return this.pdfDocument.serializeXfaData(e)}cleanup(e=!1){return this.pdfDocument.cleanup(e)}async ensure(e,t,a){(0,r.unreachable)("Abstract method `ensure` called")}requestRange(e,t){(0,r.unreachable)("Abstract method `requestRange` called")}requestLoadedStream(e=!1){(0,r.unreachable)("Abstract method `requestLoadedStream` called")}sendProgressiveData(e){(0,r.unreachable)("Abstract method `sendProgressiveData` called")}updatePassword(e){this._password=e}terminate(e){(0,r.unreachable)("Abstract method `terminate` called")}}t.LocalPdfManager=class LocalPdfManager extends BasePdfManager{constructor(e){super(e);const t=new o.Stream(e.source);this.pdfDocument=new s.PDFDocument(this,t);this._loadedStreamPromise=Promise.resolve(t)}async ensure(e,t,a){const r=e[t];return"function"==typeof r?r.apply(e,a):r}requestRange(e,t){return Promise.resolve()}requestLoadedStream(e=!1){return this._loadedStreamPromise}terminate(e){}};t.NetworkPdfManager=class NetworkPdfManager extends BasePdfManager{constructor(e){super(e);this.streamManager=new n.ChunkedStreamManager(e.source,{msgHandler:e.handler,length:e.length,disableAutoFetch:e.disableAutoFetch,rangeChunkSize:e.rangeChunkSize});this.pdfDocument=new s.PDFDocument(this,this.streamManager.getStream())}async ensure(e,t,a){try{const r=e[t];return"function"==typeof r?r.apply(e,a):r}catch(r){if(!(r instanceof i.MissingDataException))throw r;await this.requestRange(r.begin,r.end);return this.ensure(e,t,a)}}requestRange(e,t){return this.streamManager.requestRange(e,t)}requestLoadedStream(e=!1){return this.streamManager.requestAllChunks(e)}sendProgressiveData(e){this.streamManager.onReceiveData({chunk:e})}terminate(e){this.streamManager.abort(e)}}},(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0});t.ChunkedStreamManager=t.ChunkedStream=void 0;var r=a(3),n=a(2),i=a(8);class ChunkedStream extends i.Stream{constructor(e,t,a){super(new Uint8Array(e),0,e,null);this.chunkSize=t;this._loadedChunks=new Set;this.numChunks=Math.ceil(e/t);this.manager=a;this.progressiveDataLength=0;this.lastSuccessfulEnsureByteChunk=-1}getMissingChunks(){const e=[];for(let t=0,a=this.numChunks;t<a;++t)this._loadedChunks.has(t)||e.push(t);return e}get numChunksLoaded(){return this._loadedChunks.size}get isDataLoaded(){return this.numChunksLoaded===this.numChunks}onReceiveData(e,t){const a=this.chunkSize;if(e%a!=0)throw new Error(`Bad begin offset: ${e}`);const r=e+t.byteLength;if(r%a!=0&&r!==this.bytes.length)throw new Error(`Bad end offset: ${r}`);this.bytes.set(new Uint8Array(t),e);const n=Math.floor(e/a),i=Math.floor((r-1)/a)+1;for(let e=n;e<i;++e)this._loadedChunks.add(e)}onReceiveProgressiveData(e){let t=this.progressiveDataLength;const a=Math.floor(t/this.chunkSize);this.bytes.set(new Uint8Array(e),t);t+=e.byteLength;this.progressiveDataLength=t;const r=t>=this.end?this.numChunks:Math.floor(t/this.chunkSize);for(let e=a;e<r;++e)this._loadedChunks.add(e)}ensureByte(e){if(e<this.progressiveDataLength)return;const t=Math.floor(e/this.chunkSize);if(!(t>this.numChunks)&&t!==this.lastSuccessfulEnsureByteChunk){if(!this._loadedChunks.has(t))throw new r.MissingDataException(e,e+1);this.lastSuccessfulEnsureByteChunk=t}}ensureRange(e,t){if(e>=t)return;if(t<=this.progressiveDataLength)return;const a=Math.floor(e/this.chunkSize);if(a>this.numChunks)return;const n=Math.min(Math.floor((t-1)/this.chunkSize)+1,this.numChunks);for(let i=a;i<n;++i)if(!this._loadedChunks.has(i))throw new r.MissingDataException(e,t)}nextEmptyChunk(e){const t=this.numChunks;for(let a=0;a<t;++a){const r=(e+a)%t;if(!this._loadedChunks.has(r))return r}return null}hasChunk(e){return this._loadedChunks.has(e)}getByte(){const e=this.pos;if(e>=this.end)return-1;e>=this.progressiveDataLength&&this.ensureByte(e);return this.bytes[this.pos++]}getBytes(e){const t=this.bytes,a=this.pos,r=this.end;if(!e){r>this.progressiveDataLength&&this.ensureRange(a,r);return t.subarray(a,r)}let n=a+e;n>r&&(n=r);n>this.progressiveDataLength&&this.ensureRange(a,n);this.pos=n;return t.subarray(a,n)}getByteRange(e,t){e<0&&(e=0);t>this.end&&(t=this.end);t>this.progressiveDataLength&&this.ensureRange(e,t);return this.bytes.subarray(e,t)}makeSubStream(e,t,a=null){t?e+t>this.progressiveDataLength&&this.ensureRange(e,e+t):e>=this.progressiveDataLength&&this.ensureByte(e);function ChunkedStreamSubstream(){}ChunkedStreamSubstream.prototype=Object.create(this);ChunkedStreamSubstream.prototype.getMissingChunks=function(){const e=this.chunkSize,t=Math.floor(this.start/e),a=Math.floor((this.end-1)/e)+1,r=[];for(let e=t;e<a;++e)this._loadedChunks.has(e)||r.push(e);return r};Object.defineProperty(ChunkedStreamSubstream.prototype,"isDataLoaded",{get(){return this.numChunksLoaded===this.numChunks||0===this.getMissingChunks().length},configurable:!0});const r=new ChunkedStreamSubstream;r.pos=r.start=e;r.end=e+t||this.end;r.dict=a;return r}getBaseStreams(){return[this]}}t.ChunkedStream=ChunkedStream;t.ChunkedStreamManager=class ChunkedStreamManager{constructor(e,t){this.length=t.length;this.chunkSize=t.rangeChunkSize;this.stream=new ChunkedStream(this.length,this.chunkSize,this);this.pdfNetworkStream=e;this.disableAutoFetch=t.disableAutoFetch;this.msgHandler=t.msgHandler;this.currRequestId=0;this._chunksNeededByRequest=new Map;this._requestsByChunk=new Map;this._promisesByRequest=new Map;this.progressiveDataLength=0;this.aborted=!1;this._loadedStreamCapability=new n.PromiseCapability}sendRequest(e,t){const a=this.pdfNetworkStream.getRangeReader(e,t);a.isStreamingSupported||(a.onProgress=this.onProgress.bind(this));let n=[],i=0;return new Promise(((e,t)=>{const readChunk=({value:s,done:o})=>{try{if(o){const t=(0,r.arrayBuffersToBytes)(n);n=null;e(t);return}i+=s.byteLength;a.isStreamingSupported&&this.onProgress({loaded:i});n.push(s);a.read().then(readChunk,t)}catch(e){t(e)}};a.read().then(readChunk,t)})).then((t=>{this.aborted||this.onReceiveData({chunk:t,begin:e})}))}requestAllChunks(e=!1){if(!e){const e=this.stream.getMissingChunks();this._requestChunks(e)}return this._loadedStreamCapability.promise}_requestChunks(e){const t=this.currRequestId++,a=new Set;this._chunksNeededByRequest.set(t,a);for(const t of e)this.stream.hasChunk(t)||a.add(t);if(0===a.size)return Promise.resolve();const r=new n.PromiseCapability;this._promisesByRequest.set(t,r);const i=[];for(const e of a){let a=this._requestsByChunk.get(e);if(!a){a=[];this._requestsByChunk.set(e,a);i.push(e)}a.push(t)}if(i.length>0){const e=this.groupChunks(i);for(const t of e){const e=t.beginChunk*this.chunkSize,a=Math.min(t.endChunk*this.chunkSize,this.length);this.sendRequest(e,a).catch(r.reject)}}return r.promise.catch((e=>{if(!this.aborted)throw e}))}getStream(){return this.stream}requestRange(e,t){t=Math.min(t,this.length);const a=this.getBeginChunk(e),r=this.getEndChunk(t),n=[];for(let e=a;e<r;++e)n.push(e);return this._requestChunks(n)}requestRanges(e=[]){const t=[];for(const a of e){const e=this.getBeginChunk(a.begin),r=this.getEndChunk(a.end);for(let a=e;a<r;++a)t.includes(a)||t.push(a)}t.sort((function(e,t){return e-t}));return this._requestChunks(t)}groupChunks(e){const t=[];let a=-1,r=-1;for(let n=0,i=e.length;n<i;++n){const i=e[n];a<0&&(a=i);if(r>=0&&r+1!==i){t.push({beginChunk:a,endChunk:r+1});a=i}n+1===e.length&&t.push({beginChunk:a,endChunk:i+1});r=i}return t}onProgress(e){this.msgHandler.send("DocProgress",{loaded:this.stream.numChunksLoaded*this.chunkSize+e.loaded,total:this.length})}onReceiveData(e){const t=e.chunk,a=void 0===e.begin,r=a?this.progressiveDataLength:e.begin,n=r+t.byteLength,i=Math.floor(r/this.chunkSize),s=n<this.length?Math.floor(n/this.chunkSize):Math.ceil(n/this.chunkSize);if(a){this.stream.onReceiveProgressiveData(t);this.progressiveDataLength=n}else this.stream.onReceiveData(r,t);this.stream.isDataLoaded&&this._loadedStreamCapability.resolve(this.stream);const o=[];for(let e=i;e<s;++e){const t=this._requestsByChunk.get(e);if(t){this._requestsByChunk.delete(e);for(const a of t){const t=this._chunksNeededByRequest.get(a);t.has(e)&&t.delete(e);t.size>0||o.push(a)}}}if(!this.disableAutoFetch&&0===this._requestsByChunk.size){let e;if(1===this.stream.numChunksLoaded){const t=this.stream.numChunks-1;this.stream.hasChunk(t)||(e=t)}else e=this.stream.nextEmptyChunk(s);Number.isInteger(e)&&this._requestChunks([e])}for(const e of o){const t=this._promisesByRequest.get(e);this._promisesByRequest.delete(e);t.resolve()}this.msgHandler.send("DocProgress",{loaded:this.stream.numChunksLoaded*this.chunkSize,total:this.length})}onError(e){this._loadedStreamCapability.reject(e)}getBeginChunk(e){return Math.floor(e/this.chunkSize)}getEndChunk(e){return Math.floor((e-1)/this.chunkSize)+1}abort(e){this.aborted=!0;this.pdfNetworkStream?.cancelAllRequests(e);for(const t of this._promisesByRequest.values())t.reject(e)}}},(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0});t.StringStream=t.Stream=t.NullStream=void 0;var r=a(5),n=a(2);class Stream extends r.BaseStream{constructor(e,t,a,r){super();this.bytes=e instanceof Uint8Array?e:new Uint8Array(e);this.start=t||0;this.pos=this.start;this.end=t+a||this.bytes.length;this.dict=r}get length(){return this.end-this.start}get isEmpty(){return 0===this.length}getByte(){return this.pos>=this.end?-1:this.bytes[this.pos++]}getBytes(e){const t=this.bytes,a=this.pos,r=this.end;if(!e)return t.subarray(a,r);let n=a+e;n>r&&(n=r);this.pos=n;return t.subarray(a,n)}getByteRange(e,t){e<0&&(e=0);t>this.end&&(t=this.end);return this.bytes.subarray(e,t)}reset(){this.pos=this.start}moveStart(){this.start=this.pos}makeSubStream(e,t,a=null){return new Stream(this.bytes.buffer,e,t,a)}}t.Stream=Stream;t.StringStream=class StringStream extends Stream{constructor(e){super((0,n.stringToBytes)(e))}};t.NullStream=class NullStream extends Stream{constructor(){super(new Uint8Array(0))}}},(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0});t.Page=t.PDFDocument=void 0;var r=a(2),n=a(10),i=a(3),s=a(4),o=a(51),c=a(5),l=a(74),h=a(66),u=a(68),d=a(102),f=a(16),g=a(8),p=a(76),m=a(64),b=a(13),y=a(18),w=a(72),S=a(73),x=a(77),C=a(103);const k=[0,0,612,792];class Page{constructor({pdfManager:e,xref:t,pageIndex:a,pageDict:r,ref:n,globalIdFactory:i,fontCache:s,builtInCMapCache:o,standardFontDataCache:c,globalImageCache:l,systemFontCache:h,nonBlendModesSet:u,xfaFactory:d}){this.pdfManager=e;this.pageIndex=a;this.pageDict=r;this.xref=t;this.ref=n;this.fontCache=s;this.builtInCMapCache=o;this.standardFontDataCache=c;this.globalImageCache=l;this.systemFontCache=h;this.nonBlendModesSet=u;this.evaluatorOptions=e.evaluatorOptions;this.resourcesPromise=null;this.xfaFactory=d;const f={obj:0};this._localIdFactory=class extends i{static createObjId(){return`p${a}_${++f.obj}`}static getPageObjId(){return`p${n.toString()}`}}}_getInheritableProperty(e,t=!1){const a=(0,i.getInheritableProperty)({dict:this.pageDict,key:e,getArray:t,stopWhenFound:!1});return Array.isArray(a)?1!==a.length&&a[0]instanceof s.Dict?s.Dict.merge({xref:this.xref,dictArray:a}):a[0]:a}get content(){return this.pageDict.getArray("Contents")}get resources(){const e=this._getInheritableProperty("Resources");return(0,r.shadow)(this,"resources",e instanceof s.Dict?e:s.Dict.empty)}_getBoundingBox(e){if(this.xfaData)return this.xfaData.bbox;let t=this._getInheritableProperty(e,!0);if(Array.isArray(t)&&4===t.length){t=r.Util.normalizeRect(t);if(t[2]-t[0]>0&&t[3]-t[1]>0)return t;(0,r.warn)(`Empty, or invalid, /${e} entry.`)}return null}get mediaBox(){return(0,r.shadow)(this,"mediaBox",this._getBoundingBox("MediaBox")||k)}get cropBox(){return(0,r.shadow)(this,"cropBox",this._getBoundingBox("CropBox")||this.mediaBox)}get userUnit(){let e=this.pageDict.get("UserUnit");("number"!=typeof e||e<=0)&&(e=1);return(0,r.shadow)(this,"userUnit",e)}get view(){const{cropBox:e,mediaBox:t}=this;if(e!==t&&!(0,r.isArrayEqual)(e,t)){const a=r.Util.intersect(e,t);if(a&&a[2]-a[0]>0&&a[3]-a[1]>0)return(0,r.shadow)(this,"view",a);(0,r.warn)("Empty /CropBox and /MediaBox intersection.")}return(0,r.shadow)(this,"view",t)}get rotate(){let e=this._getInheritableProperty("Rotate")||0;e%90!=0?e=0:e>=360?e%=360:e<0&&(e=(e%360+360)%360);return(0,r.shadow)(this,"rotate",e)}_onSubStreamError(e,t){if(!this.evaluatorOptions.ignoreErrors)throw e;(0,r.warn)(`getContentStream - ignoring sub-stream (${t}): "${e}".`)}getContentStream(){return this.pdfManager.ensure(this,"content").then((e=>e instanceof c.BaseStream?e:Array.isArray(e)?new y.StreamsSequenceStream(e,this._onSubStreamError.bind(this)):new g.NullStream))}get xfaData(){return(0,r.shadow)(this,"xfaData",this.xfaFactory?{bbox:this.xfaFactory.getBoundingBox(this.pageIndex)}:null)}#t(e,t,a){for(const n of e)if(n.id){const e=s.Ref.fromString(n.id);if(!e){(0,r.warn)(`A non-linked annotation cannot be modified: ${n.id}`);continue}if(n.deleted){t.put(e);continue}a?.put(e);n.ref=e;delete n.id}}async saveNewAnnotations(e,t,a,r){if(this.xfaFactory)throw new Error("XFA: Cannot save new annotations.");const i=new b.PartialEvaluator({xref:this.xref,handler:e,pageIndex:this.pageIndex,idFactory:this._localIdFactory,fontCache:this.fontCache,builtInCMapCache:this.builtInCMapCache,standardFontDataCache:this.standardFontDataCache,globalImageCache:this.globalImageCache,systemFontCache:this.systemFontCache,options:this.evaluatorOptions}),o=new s.RefSet,c=new s.RefSet;this.#t(a,o,c);const l=this.pageDict,h=this.annotations.filter((e=>!(e instanceof s.Ref&&o.has(e)))),u=await n.AnnotationFactory.saveNewAnnotations(i,t,a,r);for(const{ref:e}of u.annotations)e instanceof s.Ref&&!c.has(e)&&h.push(e);const d=l.get("Annots");l.set("Annots",h);const f=[];await(0,S.writeObject)(this.ref,l,f,this.xref);d&&l.set("Annots",d);const g=u.dependencies;g.push({ref:this.ref,data:f.join("")},...u.annotations);return g}save(e,t,a){const n=new b.PartialEvaluator({xref:this.xref,handler:e,pageIndex:this.pageIndex,idFactory:this._localIdFactory,fontCache:this.fontCache,builtInCMapCache:this.builtInCMapCache,standardFontDataCache:this.standardFontDataCache,globalImageCache:this.globalImageCache,systemFontCache:this.systemFontCache,options:this.evaluatorOptions});return this._parsedAnnotations.then((function(e){const i=[];for(const s of e)s.mustBePrinted(a)&&i.push(s.save(n,t,a).catch((function(e){(0,r.warn)(`save - ignoring annotation data during "${t.name}" task: "${e}".`);return null})));return Promise.all(i).then((function(e){return e.filter((e=>!!e))}))}))}loadResources(e){this.resourcesPromise||(this.resourcesPromise=this.pdfManager.ensure(this,"resources"));return this.resourcesPromise.then((()=>new p.ObjectLoader(this.resources,e,this.xref).load()))}getOperatorList({handler:e,sink:t,task:a,intent:o,cacheKey:c,annotationStorage:l=null}){const h=this.getContentStream(),u=this.loadResources(["ColorSpace","ExtGState","Font","Pattern","Properties","Shading","XObject"]),d=new b.PartialEvaluator({xref:this.xref,handler:e,pageIndex:this.pageIndex,idFactory:this._localIdFactory,fontCache:this.fontCache,builtInCMapCache:this.builtInCMapCache,standardFontDataCache:this.standardFontDataCache,globalImageCache:this.globalImageCache,systemFontCache:this.systemFontCache,options:this.evaluatorOptions}),f=this.xfaFactory?null:(0,i.getNewAnnotationsMap)(l);let g=null,p=Promise.resolve(null);if(f){const e=f.get(this.pageIndex);if(e){const t=this.pdfManager.ensureDoc("annotationGlobals");let i;const o=new Set;for(const{bitmapId:t,bitmap:a}of e)!t||a||o.has(t)||o.add(t);const{isOffscreenCanvasSupported:c}=this.evaluatorOptions;if(o.size>0){const t=e.slice();for(const[e,a]of l)e.startsWith(r.AnnotationEditorPrefix)&&a.bitmap&&o.has(a.bitmapId)&&t.push(a);i=n.AnnotationFactory.generateImages(t,this.xref,c)}else i=n.AnnotationFactory.generateImages(e,this.xref,c);g=new s.RefSet;this.#t(e,g,null);p=t.then((t=>t?n.AnnotationFactory.printNewAnnotations(t,d,a,e,i):null))}}const y=Promise.all([h,u]).then((([r])=>{const n=new m.OperatorList(o,t);e.send("StartRenderPage",{transparency:d.hasBlendModes(this.resources,this.nonBlendModesSet),pageIndex:this.pageIndex,cacheKey:c});return d.getOperatorList({stream:r,task:a,resources:this.resources,operatorList:n}).then((function(){return n}))}));return Promise.all([y,this._parsedAnnotations,p]).then((function([e,t,n]){if(n){t=t.filter((e=>!(e.ref&&g.has(e.ref))));for(let e=0,a=n.length;e<a;e++){const r=n[e];if(r.refToReplace){const i=t.findIndex((e=>e.ref&&(0,s.isRefsEqual)(e.ref,r.refToReplace)));if(i>=0){t.splice(i,1,r);n.splice(e--,1);a--}}}t=t.concat(n)}if(0===t.length||o&r.RenderingIntentFlag.ANNOTATIONS_DISABLE){e.flush(!0);return{length:e.totalLength}}const i=!!(o&r.RenderingIntentFlag.ANNOTATIONS_FORMS),c=!!(o&r.RenderingIntentFlag.ANY),h=!!(o&r.RenderingIntentFlag.DISPLAY),u=!!(o&r.RenderingIntentFlag.PRINT),f=[];for(const e of t)(c||h&&e.mustBeViewed(l,i)||u&&e.mustBePrinted(l))&&f.push(e.getOperatorList(d,a,o,i,l).catch((function(e){(0,r.warn)(`getOperatorList - ignoring annotation data during "${a.name}" task: "${e}".`);return{opList:null,separateForm:!1,separateCanvas:!1}})));return Promise.all(f).then((function(t){let a=!1,r=!1;for(const{opList:n,separateForm:i,separateCanvas:s}of t){e.addOpList(n);a||=i;r||=s}e.flush(!0,{form:a,canvas:r});return{length:e.totalLength}}))}))}extractTextContent({handler:e,task:t,includeMarkedContent:a,disableNormalization:r,sink:n}){const i=this.getContentStream(),s=this.loadResources(["ExtGState","Font","Properties","XObject"]);return Promise.all([i,s]).then((([i])=>new b.PartialEvaluator({xref:this.xref,handler:e,pageIndex:this.pageIndex,idFactory:this._localIdFactory,fontCache:this.fontCache,builtInCMapCache:this.builtInCMapCache,standardFontDataCache:this.standardFontDataCache,globalImageCache:this.globalImageCache,systemFontCache:this.systemFontCache,options:this.evaluatorOptions}).getTextContent({stream:i,task:t,resources:this.resources,includeMarkedContent:a,disableNormalization:r,sink:n,viewBox:this.view})))}async getStructTree(){const e=await this.pdfManager.ensureCatalog("structTreeRoot");if(!e)return null;await this._parsedAnnotations;return(await this.pdfManager.ensure(this,"_parseStructTree",[e])).serializable}_parseStructTree(e){const t=new w.StructTreePage(e,this.pageDict);t.parse(this.ref);return t}async getAnnotationsData(e,t,a){const n=await this._parsedAnnotations;if(0===n.length)return n;const i=[],s=[];let o;const c=!!(a&r.RenderingIntentFlag.ANY),l=!!(a&r.RenderingIntentFlag.DISPLAY),h=!!(a&r.RenderingIntentFlag.PRINT);for(const a of n){const n=c||l&&a.viewable;(n||h&&a.printable)&&i.push(a.data);if(a.hasTextContent&&n){o||=new b.PartialEvaluator({xref:this.xref,handler:e,pageIndex:this.pageIndex,idFactory:this._localIdFactory,fontCache:this.fontCache,builtInCMapCache:this.builtInCMapCache,standardFontDataCache:this.standardFontDataCache,globalImageCache:this.globalImageCache,systemFontCache:this.systemFontCache,options:this.evaluatorOptions});s.push(a.extractTextContent(o,t,[-1/0,-1/0,1/0,1/0]).catch((function(e){(0,r.warn)(`getAnnotationsData - ignoring textContent during "${t.name}" task: "${e}".`)})))}}await Promise.all(s);return i}get annotations(){const e=this._getInheritableProperty("Annots");return(0,r.shadow)(this,"annotations",Array.isArray(e)?e:[])}get _parsedAnnotations(){const e=this.pdfManager.ensure(this,"annotations").then((async e=>{if(0===e.length)return e;const t=await this.pdfManager.ensureDoc("annotationGlobals");if(!t)return[];const a=[];for(const i of e)a.push(n.AnnotationFactory.create(this.xref,i,t,this._localIdFactory,!1,this.ref).catch((function(e){(0,r.warn)(`_parsedAnnotations: "${e}".`);return null})));const i=[];let s;for(const e of await Promise.all(a))e&&(e instanceof n.PopupAnnotation?(s||=[]).push(e):i.push(e));s&&i.push(...s);return i}));return(0,r.shadow)(this,"_parsedAnnotations",e)}get jsActions(){const e=(0,i.collectActions)(this.xref,this.pageDict,r.PageActionEventType);return(0,r.shadow)(this,"jsActions",e)}}t.Page=Page;const v=new Uint8Array([37,80,68,70,45]),F=new Uint8Array([115,116,97,114,116,120,114,101,102]),O=new Uint8Array([101,110,100,111,98,106]);function find(e,t,a=1024,r=!1){const n=t.length,i=e.peekBytes(a),s=i.length-n;if(s<=0)return!1;if(r){const a=n-1;let r=i.length-1;for(;r>=a;){let s=0;for(;s<n&&i[r-s]===t[a-s];)s++;if(s>=n){e.pos+=r-a;return!0}r--}}else{let a=0;for(;a<=s;){let r=0;for(;r<n&&i[a+r]===t[r];)r++;if(r>=n){e.pos+=a;return!0}a++}}return!1}t.PDFDocument=class PDFDocument{constructor(e,t){if(t.length<=0)throw new r.InvalidPDFException("The PDF file is empty, i.e. its size is zero bytes.");this.pdfManager=e;this.stream=t;this.xref=new C.XRef(t,e);this._pagePromises=new Map;this._version=null;const a={font:0};this._globalIdFactory=class{static getDocId(){return`g_${e.docId}`}static createFontId(){return"f"+ ++a.font}static createObjId(){(0,r.unreachable)("Abstract method `createObjId` called.")}static getPageObjId(){(0,r.unreachable)("Abstract method `getPageObjId` called.")}}}parse(e){this.xref.parse(e);this.catalog=new h.Catalog(this.pdfManager,this.xref)}get linearization(){let e=null;try{e=f.Linearization.create(this.stream)}catch(e){if(e instanceof i.MissingDataException)throw e;(0,r.info)(e)}return(0,r.shadow)(this,"linearization",e)}get startXRef(){const e=this.stream;let t=0;if(this.linearization){e.reset();find(e,O)&&(t=e.pos+6-e.start)}else{const a=1024,r=F.length;let n=!1,s=e.end;for(;!n&&s>0;){s-=a-r;s<0&&(s=0);e.pos=s;n=find(e,F,a,!0)}if(n){e.skip(9);let a;do{a=e.getByte()}while((0,i.isWhiteSpace)(a));let r="";for(;a>=32&&a<=57;){r+=String.fromCharCode(a);a=e.getByte()}t=parseInt(r,10);isNaN(t)&&(t=0)}}return(0,r.shadow)(this,"startXRef",t)}checkHeader(){const e=this.stream;e.reset();if(!find(e,v))return;e.moveStart();e.skip(v.length);let t,a="";for(;(t=e.getByte())>32&&a.length<7;)a+=String.fromCharCode(t);i.PDF_VERSION_REGEXP.test(a)?this._version=a:(0,r.warn)(`Invalid PDF header version: ${a}`)}parseStartXRef(){this.xref.setStartXRef(this.startXRef)}get numPages(){let e=0;e=this.catalog.hasActualNumPages?this.catalog.numPages:this.xfaFactory?this.xfaFactory.getNumPages():this.linearization?this.linearization.numPages:this.catalog.numPages;return(0,r.shadow)(this,"numPages",e)}_hasOnlyDocumentSignatures(e,t=0){return!!Array.isArray(e)&&e.every((e=>{if(!((e=this.xref.fetchIfRef(e))instanceof s.Dict))return!1;if(e.has("Kids")){if(++t>10){(0,r.warn)("_hasOnlyDocumentSignatures: maximum recursion depth reached");return!1}return this._hasOnlyDocumentSignatures(e.get("Kids"),t)}const a=(0,s.isName)(e.get("FT"),"Sig"),n=e.get("Rect"),i=Array.isArray(n)&&n.every((e=>0===e));return a&&i}))}get _xfaStreams(){const e=this.catalog.acroForm;if(!e)return null;const t=e.get("XFA"),a={"xdp:xdp":"",template:"",datasets:"",config:"",connectionSet:"",localeSet:"",stylesheet:"","/xdp:xdp":""};if(t instanceof c.BaseStream&&!t.isEmpty){a["xdp:xdp"]=t;return a}if(!Array.isArray(t)||0===t.length)return null;for(let e=0,r=t.length;e<r;e+=2){let n;n=0===e?"xdp:xdp":e===r-2?"/xdp:xdp":t[e];if(!a.hasOwnProperty(n))continue;const i=this.xref.fetchIfRef(t[e+1]);i instanceof c.BaseStream&&!i.isEmpty&&(a[n]=i)}return a}get xfaDatasets(){const e=this._xfaStreams;if(!e)return(0,r.shadow)(this,"xfaDatasets",null);for(const t of["datasets","xdp:xdp"]){const a=e[t];if(a)try{const e={[t]:(0,r.stringToUTF8String)(a.getString())};return(0,r.shadow)(this,"xfaDatasets",new d.DatasetReader(e))}catch{(0,r.warn)("XFA - Invalid utf-8 string.");break}}return(0,r.shadow)(this,"xfaDatasets",null)}get xfaData(){const e=this._xfaStreams;if(!e)return null;const t=Object.create(null);for(const[a,n]of Object.entries(e))if(n)try{t[a]=(0,r.stringToUTF8String)(n.getString())}catch{(0,r.warn)("XFA - Invalid utf-8 string.");return null}return t}get xfaFactory(){let e;this.pdfManager.enableXfa&&this.catalog.needsRendering&&this.formInfo.hasXfa&&!this.formInfo.hasAcroForm&&(e=this.xfaData);return(0,r.shadow)(this,"xfaFactory",e?new x.XFAFactory(e):null)}get isPureXfa(){return!!this.xfaFactory&&this.xfaFactory.isValid()}get htmlForXfa(){return this.xfaFactory?this.xfaFactory.getPages():null}async loadXfaImages(){const e=await this.pdfManager.ensureCatalog("xfaImages");if(!e)return;const t=e.getKeys(),a=new p.ObjectLoader(e,t,this.xref);await a.load();const r=new Map;for(const a of t){const t=e.get(a);t instanceof c.BaseStream&&r.set(a,t.getBytes())}this.xfaFactory.setImages(r)}async loadXfaFonts(e,t){const a=await this.pdfManager.ensureCatalog("acroForm");if(!a)return;const n=await a.getAsync("DR");if(!(n instanceof s.Dict))return;const c=new p.ObjectLoader(n,["Font"],this.xref);await c.load();const l=n.get("Font");if(!(l instanceof s.Dict))return;const h=Object.assign(Object.create(null),this.pdfManager.evaluatorOptions);h.useSystemFonts=!1;const u=new b.PartialEvaluator({xref:this.xref,handler:e,pageIndex:-1,idFactory:this._globalIdFactory,fontCache:this.catalog.fontCache,builtInCMapCache:this.catalog.builtInCMapCache,standardFontDataCache:this.catalog.standardFontDataCache,options:h}),d=new m.OperatorList,f=[],g={get font(){return f.at(-1)},set font(e){f.push(e)},clone(){return this}},y=new Map;l.forEach(((e,t)=>{y.set(e,t)}));const w=[];for(const[e,a]of y){const o=a.get("FontDescriptor");if(!(o instanceof s.Dict))continue;let c=o.get("FontFamily");c=c.replaceAll(/[ ]+(\d)/g,"$1");const l={fontFamily:c,fontWeight:o.get("FontWeight"),italicAngle:-o.get("ItalicAngle")};(0,i.validateCSSFont)(l)&&w.push(u.handleSetFont(n,[s.Name.get(e),1],null,d,t,g,null,l).catch((function(e){(0,r.warn)(`loadXfaFonts: "${e}".`);return null})))}await Promise.all(w);const S=this.xfaFactory.setFonts(f);if(!S)return;h.ignoreErrors=!0;w.length=0;f.length=0;const x=new Set;for(const e of S)(0,o.getXfaFontName)(`${e}-Regular`)||x.add(e);x.size&&S.push("PdfJS-Fallback");for(const e of S)if(!x.has(e))for(const a of[{name:"Regular",fontWeight:400,italicAngle:0},{name:"Bold",fontWeight:700,italicAngle:0},{name:"Italic",fontWeight:400,italicAngle:12},{name:"BoldItalic",fontWeight:700,italicAngle:12}]){const i=`${e}-${a.name}`,c=(0,o.getXfaFontDict)(i);w.push(u.handleSetFont(n,[s.Name.get(i),1],null,d,t,g,c,{fontFamily:e,fontWeight:a.fontWeight,italicAngle:a.italicAngle}).catch((function(e){(0,r.warn)(`loadXfaFonts: "${e}".`);return null})))}await Promise.all(w);this.xfaFactory.appendFonts(f,x)}async serializeXfaData(e){return this.xfaFactory?this.xfaFactory.serializeData(e):null}get version(){return this.catalog.version||this._version}get formInfo(){const e={hasFields:!1,hasAcroForm:!1,hasXfa:!1,hasSignatures:!1},t=this.catalog.acroForm;if(!t)return(0,r.shadow)(this,"formInfo",e);try{const a=t.get("Fields"),r=Array.isArray(a)&&a.length>0;e.hasFields=r;const n=t.get("XFA");e.hasXfa=Array.isArray(n)&&n.length>0||n instanceof c.BaseStream&&!n.isEmpty;const i=!!(1&t.get("SigFlags")),s=i&&this._hasOnlyDocumentSignatures(a);e.hasAcroForm=r&&!s;e.hasSignatures=i}catch(e){if(e instanceof i.MissingDataException)throw e;(0,r.warn)(`Cannot fetch form information: "${e}".`)}return(0,r.shadow)(this,"formInfo",e)}get documentInfo(){const e={PDFFormatVersion:this.version,Language:this.catalog.lang,EncryptFilterName:this.xref.encrypt?this.xref.encrypt.filterName:null,IsLinearized:!!this.linearization,IsAcroFormPresent:this.formInfo.hasAcroForm,IsXFAPresent:this.formInfo.hasXfa,IsCollectionPresent:!!this.catalog.collection,IsSignaturesPresent:this.formInfo.hasSignatures};let t;try{t=this.xref.trailer.get("Info")}catch(e){if(e instanceof i.MissingDataException)throw e;(0,r.info)("The document information dictionary is invalid.")}if(!(t instanceof s.Dict))return(0,r.shadow)(this,"documentInfo",e);for(const a of t.getKeys()){const n=t.get(a);switch(a){case"Title":case"Author":case"Subject":case"Keywords":case"Creator":case"Producer":case"CreationDate":case"ModDate":if("string"==typeof n){e[a]=(0,r.stringToPDFString)(n);continue}break;case"Trapped":if(n instanceof s.Name){e[a]=n;continue}break;default:let t;switch(typeof n){case"string":t=(0,r.stringToPDFString)(n);break;case"number":case"boolean":t=n;break;default:n instanceof s.Name&&(t=n)}if(void 0===t){(0,r.warn)(`Bad value, for custom key "${a}", in Info: ${n}.`);continue}e.Custom||(e.Custom=Object.create(null));e.Custom[a]=t;continue}(0,r.warn)(`Bad value, for key "${a}", in Info: ${n}.`)}return(0,r.shadow)(this,"documentInfo",e)}get fingerprints(){function validate(e){return"string"==typeof e&&e.length>0&&"\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"!==e}function hexString(e){const t=[];for(const a of e){const e=a.toString(16);t.push(e.padStart(2,"0"))}return t.join("")}const e=this.xref.trailer.get("ID");let t,a;if(Array.isArray(e)&&validate(e[0])){t=(0,r.stringToBytes)(e[0]);e[1]!==e[0]&&validate(e[1])&&(a=(0,r.stringToBytes)(e[1]))}else t=(0,l.calculateMD5)(this.stream.getByteRange(0,1024),0,1024);return(0,r.shadow)(this,"fingerprints",[hexString(t),a?hexString(a):null])}async _getLinearizationPage(e){const{catalog:t,linearization:a,xref:n}=this,i=s.Ref.get(a.objectNumberFirst,0);try{const e=await n.fetchAsync(i);if(e instanceof s.Dict){let a=e.getRaw("Type");a instanceof s.Ref&&(a=await n.fetchAsync(a));if((0,s.isName)(a,"Page")||!e.has("Type")&&!e.has("Kids")){t.pageKidsCountCache.has(i)||t.pageKidsCountCache.put(i,1);t.pageIndexCache.has(i)||t.pageIndexCache.put(i,0);return[e,i]}}throw new r.FormatError("The Linearization dictionary doesn't point to a valid Page dictionary.")}catch(a){(0,r.warn)(`_getLinearizationPage: "${a.message}".`);return t.getPageDict(e)}}getPage(e){const t=this._pagePromises.get(e);if(t)return t;const{catalog:a,linearization:r,xfaFactory:n}=this;let i;i=n?Promise.resolve([s.Dict.empty,null]):r?.pageFirst===e?this._getLinearizationPage(e):a.getPageDict(e);i=i.then((([t,r])=>new Page({pdfManager:this.pdfManager,xref:this.xref,pageIndex:e,pageDict:t,ref:r,globalIdFactory:this._globalIdFactory,fontCache:a.fontCache,builtInCMapCache:a.builtInCMapCache,standardFontDataCache:a.standardFontDataCache,globalImageCache:a.globalImageCache,systemFontCache:a.systemFontCache,nonBlendModesSet:a.nonBlendModesSet,xfaFactory:n})));this._pagePromises.set(e,i);return i}async checkFirstPage(e=!1){if(!e)try{await this.getPage(0)}catch(e){if(e instanceof i.XRefEntryException){this._pagePromises.delete(0);await this.cleanup();throw new i.XRefParseException}}}async checkLastPage(e=!1){const{catalog:t,pdfManager:a}=this;t.setActualNumPages();let n;try{await Promise.all([a.ensureDoc("xfaFactory"),a.ensureDoc("linearization"),a.ensureCatalog("numPages")]);if(this.xfaFactory)return;n=this.linearization?this.linearization.numPages:t.numPages;if(!Number.isInteger(n))throw new r.FormatError("Page count is not an integer.");if(n<=1)return;await this.getPage(n-1)}catch(s){this._pagePromises.delete(n-1);await this.cleanup();if(s instanceof i.XRefEntryException&&!e)throw new i.XRefParseException;(0,r.warn)(`checkLastPage - invalid /Pages tree /Count: ${n}.`);let o;try{o=await t.getAllPageDicts(e)}catch(a){if(a instanceof i.XRefEntryException&&!e)throw new i.XRefParseException;t.setActualNumPages(1);return}for(const[e,[r,n]]of o){let i;if(r instanceof Error){i=Promise.reject(r);i.catch((()=>{}))}else i=Promise.resolve(new Page({pdfManager:a,xref:this.xref,pageIndex:e,pageDict:r,ref:n,globalIdFactory:this._globalIdFactory,fontCache:t.fontCache,builtInCMapCache:t.builtInCMapCache,standardFontDataCache:t.standardFontDataCache,globalImageCache:t.globalImageCache,systemFontCache:t.systemFontCache,nonBlendModesSet:t.nonBlendModesSet,xfaFactory:null}));this._pagePromises.set(e,i)}t.setActualNumPages(o.size)}}fontFallback(e,t){return this.catalog.fontFallback(e,t)}async cleanup(e=!1){return this.catalog?this.catalog.cleanup(e):(0,u.clearGlobalCaches)()}#a(e,t,a,i){const s=this.xref.fetchIfRef(t);if(s.has("T")){const t=(0,r.stringToPDFString)(s.get("T"));e=""===e?t:`${e}.${t}`}a.has(e)||a.set(e,[]);a.get(e).push(n.AnnotationFactory.create(this.xref,t,i,this._localIdFactory,!0,null).then((e=>e?.getFieldObject())).catch((function(e){(0,r.warn)(`#collectFieldObjects: "${e}".`);return null})));if(s.has("Kids"))for(const t of s.get("Kids"))this.#a(e,t,a,i)}get fieldObjects(){if(!this.formInfo.hasFields)return(0,r.shadow)(this,"fieldObjects",Promise.resolve(null));const e=this.pdfManager.ensureDoc("annotationGlobals").then((async e=>{if(!e)return null;const t=Object.create(null),a=new Map;for(const t of this.catalog.acroForm.get("Fields"))this.#a("",t,a,e);const r=[];for(const[e,n]of a)r.push(Promise.all(n).then((a=>{(a=a.filter((e=>!!e))).length>0&&(t[e]=a)})));await Promise.all(r);return t}));return(0,r.shadow)(this,"fieldObjects",e)}get hasJSActions(){const e=this.pdfManager.ensureDoc("_parseHasJSActions");return(0,r.shadow)(this,"hasJSActions",e)}async _parseHasJSActions(){const[e,t]=await Promise.all([this.pdfManager.ensureCatalog("jsActions"),this.pdfManager.ensureDoc("fieldObjects")]);return!!e||!!t&&Object.values(t).some((e=>e.some((e=>null!==e.actions))))}get calculationOrderIds(){const e=this.catalog.acroForm;if(!e?.has("CO"))return(0,r.shadow)(this,"calculationOrderIds",null);const t=e.get("CO");if(!Array.isArray(t)||0===t.length)return(0,r.shadow)(this,"calculationOrderIds",null);const a=[];for(const e of t)e instanceof s.Ref&&a.push(e.toString());return 0===a.length?(0,r.shadow)(this,"calculationOrderIds",null):(0,r.shadow)(this,"calculationOrderIds",a)}get annotationGlobals(){return(0,r.shadow)(this,"annotationGlobals",n.AnnotationFactory.createGlobals(this.pdfManager))}}},(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0});t.PopupAnnotation=t.MarkupAnnotation=t.AnnotationFactory=t.AnnotationBorderStyle=t.Annotation=void 0;t.getQuadPoints=getQuadPoints;var r=a(2),n=a(3),i=a(11),s=a(4),o=a(8),c=a(5),l=a(60),h=a(66),u=a(12),d=a(69),f=a(26),g=a(76),p=a(64),m=a(73),b=a(77);t.AnnotationFactory=class AnnotationFactory{static createGlobals(e){return Promise.all([e.ensureCatalog("acroForm"),e.ensureDoc("xfaDatasets"),e.ensureCatalog("structTreeRoot"),e.ensureCatalog("baseUrl"),e.ensureCatalog("attachments")]).then((([t,a,r,n,i])=>({pdfManager:e,acroForm:t instanceof s.Dict?t:s.Dict.empty,xfaDatasets:a,structTreeRoot:r,baseUrl:n,attachments:i})),(e=>{(0,r.warn)(`createGlobals: "${e}".`);return null}))}static async create(e,t,a,r,n,i){const s=n?await this._getPageIndex(e,t,a.pdfManager):null;return a.pdfManager.ensure(this,"_create",[e,t,a,r,n,s,i])}static _create(e,t,a,i,o=!1,c=null,l=null){const h=e.fetchIfRef(t);if(!(h instanceof s.Dict))return;const{acroForm:u,pdfManager:d}=a,f=t instanceof s.Ref?t.toString():`annot_${i.createObjId()}`;let g=h.get("Subtype");g=g instanceof s.Name?g.name:null;const p={xref:e,ref:t,dict:h,subtype:g,id:f,annotationGlobals:a,collectFields:o,needAppearances:!o&&!0===u.get("NeedAppearances"),pageIndex:c,evaluatorOptions:d.evaluatorOptions,pageRef:l};switch(g){case"Link":return new LinkAnnotation(p);case"Text":return new TextAnnotation(p);case"Widget":let e=(0,n.getInheritableProperty)({dict:h,key:"FT"});e=e instanceof s.Name?e.name:null;switch(e){case"Tx":return new TextWidgetAnnotation(p);case"Btn":return new ButtonWidgetAnnotation(p);case"Ch":return new ChoiceWidgetAnnotation(p);case"Sig":return new SignatureWidgetAnnotation(p)}(0,r.warn)(`Unimplemented widget field type "${e}", falling back to base field type.`);return new WidgetAnnotation(p);case"Popup":return new PopupAnnotation(p);case"FreeText":return new FreeTextAnnotation(p);case"Line":return new LineAnnotation(p);case"Square":return new SquareAnnotation(p);case"Circle":return new CircleAnnotation(p);case"PolyLine":return new PolylineAnnotation(p);case"Polygon":return new PolygonAnnotation(p);case"Caret":return new CaretAnnotation(p);case"Ink":return new InkAnnotation(p);case"Highlight":return new HighlightAnnotation(p);case"Underline":return new UnderlineAnnotation(p);case"Squiggly":return new SquigglyAnnotation(p);case"StrikeOut":return new StrikeOutAnnotation(p);case"Stamp":return new StampAnnotation(p);case"FileAttachment":return new FileAttachmentAnnotation(p);default:o||(g?(0,r.warn)(`Unimplemented annotation type "${g}", falling back to base annotation.`):(0,r.warn)("Annotation is missing the required /Subtype."));return new Annotation(p)}}static async _getPageIndex(e,t,a){try{const n=await e.fetchIfRefAsync(t);if(!(n instanceof s.Dict))return-1;const i=n.getRaw("P");if(i instanceof s.Ref)try{return await a.ensureCatalog("getPageIndex",[i])}catch(e){(0,r.info)(`_getPageIndex -- not a valid page reference: "${e}".`)}if(n.has("Kids"))return-1;const o=await a.ensureDoc("numPages");for(let e=0;e<o;e++){const r=await a.getPage(e),n=await a.ensure(r,"annotations");for(const a of n)if(a instanceof s.Ref&&(0,s.isRefsEqual)(a,t))return e}}catch(e){(0,r.warn)(`_getPageIndex: "${e}".`)}return-1}static generateImages(e,t,a){if(!a){(0,r.warn)("generateImages: OffscreenCanvas is not supported, cannot save or print some annotations with images.");return null}let n;for(const{bitmapId:a,bitmap:r}of e)if(r){n||=new Map;n.set(a,StampAnnotation.createImage(r,t))}return n}static async saveNewAnnotations(e,t,a,n){const i=e.xref;let o;const c=[],l=[],{isOffscreenCanvasSupported:h}=e.options;for(const u of a)if(!u.deleted)switch(u.annotationType){case r.AnnotationEditorType.FREETEXT:if(!o){const e=new s.Dict(i);e.set("BaseFont",s.Name.get("Helvetica"));e.set("Type",s.Name.get("Font"));e.set("Subtype",s.Name.get("Type1"));e.set("Encoding",s.Name.get("WinAnsiEncoding"));const t=[];o=i.getNewTemporaryRef();await(0,m.writeObject)(o,e,t,i);c.push({ref:o,data:t.join("")})}l.push(FreeTextAnnotation.createNewAnnotation(i,u,c,{evaluator:e,task:t,baseFontRef:o}));break;case r.AnnotationEditorType.INK:l.push(InkAnnotation.createNewAnnotation(i,u,c));break;case r.AnnotationEditorType.STAMP:if(!h)break;const a=await n.get(u.bitmapId);if(a.imageStream){const{imageStream:e,smaskStream:t}=a,r=[];if(t){const a=i.getNewTemporaryRef();await(0,m.writeObject)(a,t,r,i);c.push({ref:a,data:r.join("")});e.dict.set("SMask",a);r.length=0}const n=a.imageRef=i.getNewTemporaryRef();await(0,m.writeObject)(n,e,r,i);c.push({ref:n,data:r.join("")});a.imageStream=a.smaskStream=null}l.push(StampAnnotation.createNewAnnotation(i,u,c,{image:a}))}return{annotations:await Promise.all(l),dependencies:c}}static async printNewAnnotations(e,t,a,n,i){if(!n)return null;const{options:s,xref:o}=t,c=[];for(const l of n)if(!l.deleted)switch(l.annotationType){case r.AnnotationEditorType.FREETEXT:c.push(FreeTextAnnotation.createNewPrintAnnotation(e,o,l,{evaluator:t,task:a,evaluatorOptions:s}));break;case r.AnnotationEditorType.INK:c.push(InkAnnotation.createNewPrintAnnotation(e,o,l,{evaluatorOptions:s}));break;case r.AnnotationEditorType.STAMP:if(!s.isOffscreenCanvasSupported)break;const n=await i.get(l.bitmapId);if(n.imageStream){const{imageStream:e,smaskStream:t}=n;t&&e.dict.set("SMask",t);n.imageRef=new f.JpegStream(e,e.length);n.imageStream=n.smaskStream=null}c.push(StampAnnotation.createNewPrintAnnotation(e,o,l,{image:n,evaluatorOptions:s}))}return Promise.all(c)}};function getRgbColor(e,t=new Uint8ClampedArray(3)){if(!Array.isArray(e))return t;const a=t||new Uint8ClampedArray(3);switch(e.length){case 0:return null;case 1:u.ColorSpace.singletons.gray.getRgbItem(e,0,a,0);return a;case 3:u.ColorSpace.singletons.rgb.getRgbItem(e,0,a,0);return a;case 4:u.ColorSpace.singletons.cmyk.getRgbItem(e,0,a,0);return a;default:return t}}function getPdfColorArray(e){return Array.from(e,(e=>e/255))}function getQuadPoints(e,t){const a=e.getArray("QuadPoints");if(!Array.isArray(a)||0===a.length||a.length%8>0)return null;const r=[];for(let e=0,n=a.length/8;e<n;e++){let n=1/0,i=-1/0,s=1/0,o=-1/0;for(let t=8*e,r=8*e+8;t<r;t+=2){const e=a[t],r=a[t+1];n=Math.min(e,n);i=Math.max(e,i);s=Math.min(r,s);o=Math.max(r,o)}if(null!==t&&(n<t[0]||i>t[2]||s<t[1]||o>t[3]))return null;r.push([{x:n,y:o},{x:i,y:o},{x:n,y:s},{x:i,y:s}])}return r}function getTransformMatrix(e,t,a){const[n,i,s,o]=r.Util.getAxialAlignedBoundingBox(t,a);if(n===s||i===o)return[1,0,0,1,e[0],e[1]];const c=(e[2]-e[0])/(s-n),l=(e[3]-e[1])/(o-i);return[c,0,0,l,e[0]-n*c,e[1]-i*l]}class Annotation{constructor(e){const{dict:t,xref:a,annotationGlobals:i}=e;this.setTitle(t.get("T"));this.setContents(t.get("Contents"));this.setModificationDate(t.get("M"));this.setFlags(t.get("F"));this.setRectangle(t.getArray("Rect"));this.setColor(t.getArray("C"));this.setBorderStyle(t);this.setAppearance(t);this.setOptionalContent(t);const o=t.get("MK");this.setBorderAndBackgroundColors(o);this.setRotation(o,t);this.ref=e.ref instanceof s.Ref?e.ref:null;this._streams=[];this.appearance&&this._streams.push(this.appearance);const c=!!(this.flags&r.AnnotationFlag.LOCKED),l=!!(this.flags&r.AnnotationFlag.LOCKEDCONTENTS);if(i.structTreeRoot){let a=t.get("StructParent");a=Number.isInteger(a)&&a>=0?a:-1;i.structTreeRoot.addAnnotationIdToPage(e.pageRef,a)}this.data={annotationFlags:this.flags,borderStyle:this.borderStyle,color:this.color,backgroundColor:this.backgroundColor,borderColor:this.borderColor,rotation:this.rotation,contentsObj:this._contents,hasAppearance:!!this.appearance,id:e.id,modificationDate:this.modificationDate,rect:this.rectangle,subtype:e.subtype,hasOwnCanvas:!1,noRotate:!!(this.flags&r.AnnotationFlag.NOROTATE),noHTML:c&&l};if(e.collectFields){const i=t.get("Kids");if(Array.isArray(i)){const e=[];for(const t of i)t instanceof s.Ref&&e.push(t.toString());0!==e.length&&(this.data.kidIds=e)}this.data.actions=(0,n.collectActions)(a,t,r.AnnotationActionEventType);this.data.fieldName=this._constructFieldName(t);this.data.pageIndex=e.pageIndex}this._isOffscreenCanvasSupported=e.evaluatorOptions.isOffscreenCanvasSupported;this._fallbackFontDict=null;this._needAppearances=!1}_hasFlag(e,t){return!!(e&t)}_isViewable(e){return!this._hasFlag(e,r.AnnotationFlag.INVISIBLE)&&!this._hasFlag(e,r.AnnotationFlag.NOVIEW)}_isPrintable(e){return this._hasFlag(e,r.AnnotationFlag.PRINT)&&!this._hasFlag(e,r.AnnotationFlag.HIDDEN)&&!this._hasFlag(e,r.AnnotationFlag.INVISIBLE)}mustBeViewed(e,t){const a=e?.get(this.data.id)?.noView;return void 0!==a?!a:this.viewable&&!this._hasFlag(this.flags,r.AnnotationFlag.HIDDEN)}mustBePrinted(e){const t=e?.get(this.data.id)?.noPrint;return void 0!==t?!t:this.printable}get viewable(){return null!==this.data.quadPoints&&(0===this.flags||this._isViewable(this.flags))}get printable(){return null!==this.data.quadPoints&&(0!==this.flags&&this._isPrintable(this.flags))}_parseStringHelper(e){const t="string"==typeof e?(0,r.stringToPDFString)(e):"";return{str:t,dir:t&&"rtl"===(0,l.bidi)(t).dir?"rtl":"ltr"}}setDefaultAppearance(e){const{dict:t,annotationGlobals:a}=e,r=(0,n.getInheritableProperty)({dict:t,key:"DA"})||a.acroForm.get("DA");this._defaultAppearance="string"==typeof r?r:"";this.data.defaultAppearanceData=(0,i.parseDefaultAppearance)(this._defaultAppearance)}setTitle(e){this._title=this._parseStringHelper(e)}setContents(e){this._contents=this._parseStringHelper(e)}setModificationDate(e){this.modificationDate="string"==typeof e?e:null}setFlags(e){this.flags=Number.isInteger(e)&&e>0?e:0}hasFlag(e){return this._hasFlag(this.flags,e)}setRectangle(e){this.rectangle=Array.isArray(e)&&4===e.length?r.Util.normalizeRect(e):[0,0,0,0]}setColor(e){this.color=getRgbColor(e)}setLineEndings(e){this.lineEndings=["None","None"];if(Array.isArray(e)&&2===e.length)for(let t=0;t<2;t++){const a=e[t];if(a instanceof s.Name)switch(a.name){case"None":continue;case"Square":case"Circle":case"Diamond":case"OpenArrow":case"ClosedArrow":case"Butt":case"ROpenArrow":case"RClosedArrow":case"Slash":this.lineEndings[t]=a.name;continue}(0,r.warn)(`Ignoring invalid lineEnding: ${a}`)}}setRotation(e,t){this.rotation=0;let a=e instanceof s.Dict?e.get("R")||0:t.get("Rotate")||0;if(Number.isInteger(a)&&0!==a){a%=360;a<0&&(a+=360);a%90==0&&(this.rotation=a)}}setBorderAndBackgroundColors(e){if(e instanceof s.Dict){this.borderColor=getRgbColor(e.getArray("BC"),null);this.backgroundColor=getRgbColor(e.getArray("BG"),null)}else this.borderColor=this.backgroundColor=null}setBorderStyle(e){this.borderStyle=new AnnotationBorderStyle;if(e instanceof s.Dict)if(e.has("BS")){const t=e.get("BS"),a=t.get("Type");if(!a||(0,s.isName)(a,"Border")){this.borderStyle.setWidth(t.get("W"),this.rectangle);this.borderStyle.setStyle(t.get("S"));this.borderStyle.setDashArray(t.getArray("D"))}}else if(e.has("Border")){const t=e.getArray("Border");if(Array.isArray(t)&&t.length>=3){this.borderStyle.setHorizontalCornerRadius(t[0]);this.borderStyle.setVerticalCornerRadius(t[1]);this.borderStyle.setWidth(t[2],this.rectangle);4===t.length&&this.borderStyle.setDashArray(t[3],!0)}}else this.borderStyle.setWidth(0)}setAppearance(e){this.appearance=null;const t=e.get("AP");if(!(t instanceof s.Dict))return;const a=t.get("N");if(a instanceof c.BaseStream){this.appearance=a;return}if(!(a instanceof s.Dict))return;const r=e.get("AS");if(!(r instanceof s.Name&&a.has(r.name)))return;const n=a.get(r.name);n instanceof c.BaseStream&&(this.appearance=n)}setOptionalContent(e){this.oc=null;const t=e.get("OC");t instanceof s.Name?(0,r.warn)("setOptionalContent: Support for /Name-entry is not implemented."):t instanceof s.Dict&&(this.oc=t)}loadResources(e,t){return t.dict.getAsync("Resources").then((t=>{if(!t)return;return new g.ObjectLoader(t,e,t.xref).load().then((function(){return t}))}))}async getOperatorList(e,t,a,n,i){const c=this.data;let l=this.appearance;const h=!!(this.data.hasOwnCanvas&&a&r.RenderingIntentFlag.DISPLAY);if(!l){if(!h)return{opList:new p.OperatorList,separateForm:!1,separateCanvas:!1};l=new o.StringStream("");l.dict=new s.Dict}const u=l.dict,d=await this.loadResources(["ExtGState","ColorSpace","Pattern","Shading","XObject","Font"],l),f=u.getArray("BBox")||[0,0,1,1],g=u.getArray("Matrix")||[1,0,0,1,0,0],m=getTransformMatrix(c.rect,f,g),b=new p.OperatorList;let y;this.oc&&(y=await e.parseMarkedContentProps(this.oc,null));void 0!==y&&b.addOp(r.OPS.beginMarkedContentProps,["OC",y]);b.addOp(r.OPS.beginAnnotation,[c.id,c.rect,m,g,h]);await e.getOperatorList({stream:l,task:t,resources:d,operatorList:b,fallbackFontDict:this._fallbackFontDict});b.addOp(r.OPS.endAnnotation,[]);void 0!==y&&b.addOp(r.OPS.endMarkedContent,[]);this.reset();return{opList:b,separateForm:!1,separateCanvas:h}}async save(e,t,a){return null}get hasTextContent(){return!1}async extractTextContent(e,t,a){if(!this.appearance)return;const n=await this.loadResources(["ExtGState","Font","Properties","XObject"],this.appearance),i=[],s=[];let o=null;const c={desiredSize:Math.Infinity,ready:!0,enqueue(e,t){for(const t of e.items)if(void 0!==t.str){o||=t.transform.slice(-2);s.push(t.str);if(t.hasEOL){i.push(s.join(""));s.length=0}}}};await e.getTextContent({stream:this.appearance,task:t,resources:n,includeMarkedContent:!0,sink:c,viewBox:a});this.reset();s.length&&i.push(s.join(""));if(i.length>1||i[0]){const e=this.appearance.dict,t=e.getArray("BBox")||[0,0,1,1],a=e.getArray("Matrix")||[1,0,0,1,0,0],n=this.data.rect,s=getTransformMatrix(n,t,a);s[4]-=n[0];s[5]-=n[1];o=r.Util.applyTransform(o,s);o=r.Util.applyTransform(o,a);this.data.textPosition=o;this.data.textContent=i}}getFieldObject(){return this.data.kidIds?{id:this.data.id,actions:this.data.actions,name:this.data.fieldName,strokeColor:this.data.borderColor,fillColor:this.data.backgroundColor,type:"",kidIds:this.data.kidIds,page:this.data.pageIndex,rotation:this.rotation}:null}reset(){for(const e of this._streams)e.reset()}_constructFieldName(e){if(!e.has("T")&&!e.has("Parent")){(0,r.warn)("Unknown field name, falling back to empty field name.");return""}if(!e.has("Parent"))return(0,r.stringToPDFString)(e.get("T"));const t=[];e.has("T")&&t.unshift((0,r.stringToPDFString)(e.get("T")));let a=e;const n=new s.RefSet;e.objId&&n.put(e.objId);for(;a.has("Parent");){a=a.get("Parent");if(!(a instanceof s.Dict)||a.objId&&n.has(a.objId))break;a.objId&&n.put(a.objId);a.has("T")&&t.unshift((0,r.stringToPDFString)(a.get("T")))}return t.join(".")}}t.Annotation=Annotation;class AnnotationBorderStyle{constructor(){this.width=1;this.style=r.AnnotationBorderStyleType.SOLID;this.dashArray=[3];this.horizontalCornerRadius=0;this.verticalCornerRadius=0}setWidth(e,t=[0,0,0,0]){if(e instanceof s.Name)this.width=0;else if("number"==typeof e){if(e>0){const a=(t[2]-t[0])/2,n=(t[3]-t[1])/2;if(a>0&&n>0&&(e>a||e>n)){(0,r.warn)(`AnnotationBorderStyle.setWidth - ignoring width: ${e}`);e=1}}this.width=e}}setStyle(e){if(e instanceof s.Name)switch(e.name){case"S":this.style=r.AnnotationBorderStyleType.SOLID;break;case"D":this.style=r.AnnotationBorderStyleType.DASHED;break;case"B":this.style=r.AnnotationBorderStyleType.BEVELED;break;case"I":this.style=r.AnnotationBorderStyleType.INSET;break;case"U":this.style=r.AnnotationBorderStyleType.UNDERLINE}}setDashArray(e,t=!1){if(Array.isArray(e)&&e.length>0){let a=!0,r=!0;for(const t of e){if(!(+t>=0)){a=!1;break}t>0&&(r=!1)}if(a&&!r){this.dashArray=e;t&&this.setStyle(s.Name.get("D"))}else this.width=0}else e&&(this.width=0)}setHorizontalCornerRadius(e){Number.isInteger(e)&&(this.horizontalCornerRadius=e)}setVerticalCornerRadius(e){Number.isInteger(e)&&(this.verticalCornerRadius=e)}}t.AnnotationBorderStyle=AnnotationBorderStyle;class MarkupAnnotation extends Annotation{constructor(e){super(e);const{dict:t}=e;if(t.has("IRT")){const e=t.getRaw("IRT");this.data.inReplyTo=e instanceof s.Ref?e.toString():null;const a=t.get("RT");this.data.replyType=a instanceof s.Name?a.name:r.AnnotationReplyType.REPLY}let a=null;if(this.data.replyType===r.AnnotationReplyType.GROUP){const e=t.get("IRT");this.setTitle(e.get("T"));this.data.titleObj=this._title;this.setContents(e.get("Contents"));this.data.contentsObj=this._contents;if(e.has("CreationDate")){this.setCreationDate(e.get("CreationDate"));this.data.creationDate=this.creationDate}else this.data.creationDate=null;if(e.has("M")){this.setModificationDate(e.get("M"));this.data.modificationDate=this.modificationDate}else this.data.modificatio