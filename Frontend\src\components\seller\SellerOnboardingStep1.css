.seller-onboarding-step1-container {
  width: 100%;
  min-height: 100vh;
  padding: 0rem 1.5rem 1.5rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  align-items: center;
}

.seller-onboarding-step1-container .progress-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  width: 50%;
}
.seller-onboarding-step1-container .step {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--light-gray);
  color: var(--dark-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--heading6);
  border: 2px solid var(--light-gray);
  transition: background 0.3s, color 0.3s;
}
.seller-onboarding-step1-container .step.active {
  background: var(--btn-color);
  color: var(--white);
  border: 2px solid var(--btn-color);
}
.seller-onboarding-step1-container .progress-line {
  flex: 1 1 60px;
  height: 2px;
  background: var(--light-gray);
  margin: 0 0.5rem;
}

.seller-onboarding-step1-container .form-grid {
  display: grid;
  width: 100%;
  gap: 2rem;
}

.seller-onboarding-step1-container .description-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 1rem;
  position: relative;
}
.seller-onboarding-step1-container .section-title {
  font-size: var(--heading6);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 0.5rem;
  position: absolute;
  top: -20px;
  left: 50%;
  background: var(--white);
  padding: 0 0.5rem;
  transform: translate(-50%, 10px);
}
.seller-onboarding-step1-container .description-box {
  border-radius: var(--border-radius);
 
  position: relative;

  
}
.seller-onboarding-step1-container .description-textarea {
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  font-size: var(--basefont);
  background: var(--white);
}

/* About the Coach Section */
.seller-onboarding-step1-container .about-coach-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 1rem;
  position: relative;
}

.seller-onboarding-step1-container .about-coach-box {
  border-radius: var(--border-radius);
  position: relative;
}

.seller-onboarding-step1-container .about-coach-editor {
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.seller-onboarding-step1-container .profile-experience-grid {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 2rem;
}
.seller-onboarding-step1-container .profile-pic-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-self: flex-start; 
  gap: 1rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 1rem;
  position: relative;
}
.seller-onboarding-step1-container .avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}
.seller-onboarding-step1-container .avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  overflow: hidden;
  border: 2px solid var(--border-color);
}

.seller-onboarding-step1-container .avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.seller-onboarding-step1-container .upload-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.seller-onboarding-step1-container .upload-btn {
  margin-top: 0.5rem;
  font-size: var(--smallfont);
  padding: 0.5rem 1.2rem;
  min-width: 120px;
}

.seller-onboarding-step1-container .upload-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.seller-onboarding-step1-container .file-name {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.seller-onboarding-step1-container .file-status {
  font-size: var(--extrasmallfont);
  color: var(--primary-color);
  font-weight: 500;
}

.seller-onboarding-step1-container .upload-info {
  text-align: center;
  margin-top: 0.5rem;
}

.seller-onboarding-step1-container .upload-format-info {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  font-weight: 400;
  line-height: 1.3;
}

.seller-onboarding-step1-container .image-error {
  margin-top: 0.5rem;
  text-align: center;
}

/* Field error styles - consistent with parent component */
.seller-onboarding-step1-container .field-error {
  color: var(--error-color);
  font-size: var(--smallfont);
  margin-top: 0.25rem;
  font-weight: 500;
}

.seller-onboarding-step1-container .experience-section {
  display: flex;
  flex-direction: column;
  /* max-height: 100vh; */
  /* overflow-y: scroll; */
  gap: 0.75rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 1rem;
  position: relative;
}
.seller-onboarding-step1-container .experience-row {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.seller-onboarding-step1-container .experience-row-content {
  flex: 1;
  display: grid;
  gap: 0.75rem;
}

.seller-onboarding-step1-container .delete-experience-btn {
  background: transparent;
  border: 1px solid red;
  border-radius: 4px;
  color: red;
  padding: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.seller-onboarding-step1-container .delete-experience-btn:hover {
  background: red;
  color: var(--white);
  transform: scale(1.05);
}

.seller-onboarding-step1-container .delete-experience-btn:active {
  transform: scale(0.95);
}
.seller-onboarding-step1-container .input {
  flex: 1 1 120px;
  min-width: 0;
  width: 100%;
  border: 1px solid var(--light-gray);
  padding: 0.5rem 0.75rem;
  font-size: var(--basefont);
  background: var(--white);
  border-radius: var(--border-radius);
}
.seller-onboarding-step1-container .year-fields {
  display: flex;
  gap: 0.5rem;
}

.seller-onboarding-step1-container .add-more-link {
  color: var(--btn-color);
  font-size: var(--extrasmallfont);
  cursor: pointer;
  margin-top: 0.5rem;
  align-self: flex-end;
  transition: color 0.2s;
  text-align: end;
}
.seller-onboarding-step1-container .add-more-link:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.seller-onboarding-step1-container .next-btn-row {
  display: flex;
  justify-content: center;

}
.seller-onboarding-step1-container .next-btn {
  min-width: 160px;
  font-size: var(--basefont);
  border-radius: var(--border-radius-large);
}

@media (max-width: 900px) {
  .seller-onboarding-step1-container .form-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  .seller-onboarding-step1-container .profile-experience-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 600px) {
  .seller-onboarding-step1-container {
    padding: 1rem 0.5rem;
  }
  .seller-onboarding-step1-container .form-grid {
    gap: 1rem;
  }
  .seller-onboarding-step1-container .profile-experience-grid {
    gap: 1rem;
  }
  .seller-onboarding-step1-container .next-btn {
    min-width: 120px;
    font-size: var(--smallfont);
  }

}