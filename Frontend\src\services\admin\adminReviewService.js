import axios from 'axios';
import { STORAGE_KEYS } from '../../utils/constants';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/admin/reviews`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Get all reviews with filtering, sorting, and pagination
export const getAllReviews = async (params = {}) => {
  try {
    const response = await api.get('/', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get review by ID
export const getReviewById = async (id) => {
  try {
    const response = await api.get(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Delete review
export const deleteReview = async (id) => {
  try {
    const response = await api.delete(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk delete reviews
export const bulkDeleteReviews = async (reviewIds) => {
  try {
    const response = await api.post('/bulk-delete', { reviewIds });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update review status
export const updateReviewStatus = async (id, status, notes = '') => {
  try {
    const response = await api.put(`/${id}/status`, { status, notes });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk update reviews status
export const bulkUpdateReviews = async (reviewIds, status) => {
  try {
    const response = await api.post('/bulk-update', { reviewIds, status });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Flag review
export const flagReview = async (id, reason) => {
  try {
    const response = await api.put(`/${id}/flag`, { reason });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Unflag review
export const unflagReview = async (id) => {
  try {
    const response = await api.put(`/${id}/unflag`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Moderate review (approve, reject, flag)
export const moderateReview = async (id, action, reason = '') => {
  try {
    const response = await api.put(`/${id}/moderate`, { action, reason });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get review statistics
export const getReviewStats = async () => {
  try {
    const response = await api.get('/stats');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Export reviews
export const exportReviews = async (params = {}) => {
  try {
    const response = await api.get('/export', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Review management actions
export const reviewActions = {
  approve: (id) => moderateReview(id, 'approve'),
  reject: (id, reason) => moderateReview(id, 'reject', reason),
  flag: (id, reason) => flagReview(id, reason),
  unflag: (id) => unflagReview(id),
  delete: (id) => deleteReview(id)
};

export default {
  getAllReviews,
  getReviewById,
  deleteReview,
  bulkDeleteReviews,
  updateReviewStatus,
  bulkUpdateReviews,
  flagReview,
  unflagReview,
  moderateReview,
  getReviewStats,
  exportReviews,
  reviewActions
};
