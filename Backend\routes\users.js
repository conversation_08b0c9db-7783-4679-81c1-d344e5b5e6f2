const express = require("express");
const { check } = require("express-validator");
const {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  updateProfile,
  getSellerProfile,
  verifySeller,
  updateSellerOnboarding,
  getSellerOnboardingStatus,
  completeSellerOnboarding,
} = require("../controllers/users");

const { protect, authorize } = require("../middleware/auth");

const router = express.Router();

// Public routes
router.get("/sellers/:id", getSellerProfile);

// Protected routes
router.use(protect);

// Seller onboarding routes
router.get(
  "/seller/onboarding",
  authorize("seller"),
  getSellerOnboardingStatus
);

router.put(
  "/seller/onboarding",
  authorize("seller"),
  [
    check("description", "Description cannot exceed 1000 characters")
      .optional()
      .isLength({ max: 1000 }),
    check("experiences", "Experiences must be an array").optional().isArray(),
    check(
      "experiences.*.schoolName",
      "School name is required for each experience"
    )
      .optional()
      .not()
      .isEmpty(),
    check("experiences.*.position", "Position is required for each experience")
      .optional()
      .not()
      .isEmpty(),
    check("experiences.*.fromYear", "From year must be a valid year")
      .optional()
      .isInt({ min: 1900, max: new Date().getFullYear() }),
    check("experiences.*.toYear", "To year must be a valid year")
      .optional()
      .isInt({ min: 1900, max: new Date().getFullYear() + 10 }),
    check("minTrainingCost", "Minimum training cost must be a positive number")
      .optional()
      .isFloat({ min: 0 }),
    check("socialLinks.facebook", "Facebook URL must be valid")
      .optional()
      .isURL(),
    check("socialLinks.instagram", "Instagram URL must be valid")
      .optional()
      .isURL(),
    check("socialLinks.twitter", "Twitter URL must be valid")
      .optional()
      .isURL(),
    check("sports", "Sports must be an array").optional().isArray(),
    check("expertise", "Expertise must be an array").optional().isArray(),
    check("certifications", "Certifications must be an array")
      .optional()
      .isArray(),
  ],
  updateSellerOnboarding
);

router.post(
  "/seller/complete-onboarding",
  authorize("seller"),
  [
    check("description", "Description is required").not().isEmpty(),
    check("description", "Description cannot exceed 1000 characters").isLength({
      max: 1000,
    }),
    check("aboutCoach", "About the coach information is required").not().isEmpty(),
    check("aboutCoach", "About the coach information cannot exceed 2000 characters").isLength({
      max: 10000,
    }),
    check("experiences", "Experiences must be an array").isArray(),
    check("experiences", "At least one experience is required").isArray({
      min: 1,
    }),
    check(
      "experiences.*.schoolName",
      "School name is required for each experience"
    )
      .not()
      .isEmpty(),
    check("experiences.*.position", "Position is required for each experience")
      .not()
      .isEmpty(),
    check(
      "experiences.*.fromYear",
      "From year is required and must be a valid year"
    ).isInt({ min: 1900, max: new Date().getFullYear() }),
    check(
      "experiences.*.toYear",
      "To year must be a valid year and greater than from year"
    )
      .optional()
      .isInt({ min: 1900, max: new Date().getFullYear() }),
    check("minTrainingCost", "Minimum training cost is required").isFloat({
      min: 0,
    }),
    check("socialLinks", "Social links must be an object")
      .optional()
      .isObject(),
    check("socialLinks.facebook", "Facebook URL must be valid")
      .optional({ nullable: true, checkFalsy: true })
      .isURL(),
    check("socialLinks.instagram", "Instagram URL must be valid")
      .optional({ nullable: true, checkFalsy: true })
      .isURL(),
    check("socialLinks.twitter", "Twitter URL must be valid")
      .optional({ nullable: true, checkFalsy: true })
      .isURL(),
    check("sports", "Sports must be an array").optional().isArray(),
    check("expertise", "Expertise must be an array").optional().isArray(),
    check("certifications", "Certifications must be an array")
      .optional()
      .isArray(),
  ],
  completeSellerOnboarding
);

// User profile routes
router.put(
  "/profile/:id",
  [
    check("firstName", "First name is required").not().isEmpty(),
    check("lastName", "Last name is required").not().isEmpty(),
  ],
  updateProfile
);

// Admin routes
router.use(authorize("admin"));

router
  .route("/")
  .get(getUsers)
  .post(
    [
      check("firstName", "First name is required").not().isEmpty(),
      check("lastName", "Last name is required").not().isEmpty(),
      check("email", "Please include a valid email").isEmail(),
      check("mobile", "Mobile number is required").not().isEmpty(),
      check("role", "Role is required").isIn(["buyer", "seller", "admin"]),
    ],
    createUser
  );

router.route("/:id").get(getUser).put(updateUser).delete(deleteUser);

router.put("/verify-seller/:id", verifySeller);

module.exports = router;
