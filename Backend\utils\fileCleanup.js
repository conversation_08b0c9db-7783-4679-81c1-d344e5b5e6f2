const fs = require('fs');
const path = require('path');
const { getS3Instance, isS3Url } = require('./storageHelper');

/**
 * Comprehensive file cleanup utility for content deletion
 * Handles cleanup of all associated files: main content, thumbnails, and previews
 */

/**
 * Extract S3 key from URL
 * @param {string} fileUrl - The S3 URL
 * @returns {string|null} - The S3 key or null if invalid
 */
const extractS3Key = (fileUrl) => {
  if (!fileUrl || !isS3Url(fileUrl)) {
    return null;
  }

  try {
    // Handle different S3 URL formats
    if (fileUrl.includes('.amazonaws.com/')) {
      // Format: https://bucket.s3.region.amazonaws.com/key
      return fileUrl.split('.amazonaws.com/')[1];
    } else if (fileUrl.includes('amazonaws.com/')) {
      // Format: https://s3.region.amazonaws.com/bucket/key
      const parts = fileUrl.split('amazonaws.com/')[1].split('/');
      return parts.slice(1).join('/'); // Remove bucket name, keep key
    }

    return null;
  } catch (error) {
    console.error('[FileCleanup] Error extracting S3 key from URL:', fileUrl, error);
    return null;
  }
};

/**
 * Delete a single file from S3
 * @param {string} fileUrl - The S3 file URL
 * @returns {Promise<boolean>} - Whether deletion was successful
 */
const deleteS3File = async (fileUrl) => {
  try {
    const s3 = getS3Instance();
    if (!s3) {
      console.warn('[FileCleanup] S3 not available for file deletion');
      return false;
    }

    const key = extractS3Key(fileUrl);
    if (!key) {
      console.error('[FileCleanup] Could not extract S3 key from URL:', fileUrl);
      return false;
    }

    console.log(`[FileCleanup] Deleting S3 file with key: ${key}`);

    // First check if object exists
    try {
      await s3.headObject({
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: key
      }).promise();
    } catch (headError) {
      if (headError.code === 'NotFound' || headError.statusCode === 404) {
        console.log(`[FileCleanup] S3 file already deleted or doesn't exist: ${key}`);
        return true; // Consider this a success since the file is gone
      }
      throw headError;
    }

    // Delete the object
    await s3.deleteObject({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: key
    }).promise();

    // Verify deletion
    try {
      await s3.headObject({
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: key
      }).promise();
      // If we reach here, object still exists
      console.error(`[FileCleanup] S3 file still exists after deletion: ${key}`);
      return false;
    } catch (verifyError) {
      if (verifyError.code === 'NotFound' || verifyError.statusCode === 404) {
        console.log(`[FileCleanup] S3 file deleted successfully: ${key}`);
        return true;
      }
      throw verifyError;
    }

  } catch (error) {
    console.error(`[FileCleanup] Error deleting S3 file:`, error);
    return false;
  }
};

/**
 * Delete a single local file
 * @param {string} fileUrl - The local file URL
 * @returns {Promise<boolean>} - Whether deletion was successful
 */
const deleteLocalFile = async (fileUrl) => {
  try {
    if (!fileUrl) {
      return true;
    }

    // Extract filename and determine file path
    let filePath;

    if (fileUrl.startsWith('/uploads/')) {
      // Standard upload path
      filePath = path.join('.', fileUrl);
    } else {
      // Handle different local file patterns
      const fileName = fileUrl.split('/').pop();

      // Determine subdirectory based on file type or URL pattern
      if (fileUrl.includes('profile') || fileUrl.includes('profiles')) {
        filePath = path.join('./uploads/profiles/', fileName);
      } else if (fileUrl.includes('preview') || fileUrl.includes('previews')) {
        filePath = path.join('./uploads/previews/', fileName);
      } else if (fileUrl.includes('thumbnail') || fileUrl.includes('thumbnails')) {
        filePath = path.join('./uploads/thumbnails/', fileName);
      } else {
        // Default to main uploads directory
        filePath = path.join('./uploads/', fileName);
      }
    }

    console.log(`[FileCleanup] Attempting to delete local file: ${filePath}`);

    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);

      // Verify deletion
      if (!fs.existsSync(filePath)) {
        console.log(`[FileCleanup] Local file deleted successfully: ${filePath}`);
        return true;
      } else {
        console.error(`[FileCleanup] Local file still exists after deletion: ${filePath}`);
        return false;
      }
    } else {
      console.log(`[FileCleanup] Local file not found, may have been already deleted: ${filePath}`);
      return true; // Consider this a success since the file is gone
    }

  } catch (error) {
    console.error(`[FileCleanup] Error deleting local file:`, error);
    return false;
  }
};

/**
 * Delete a single file (auto-detects S3 vs local)
 * @param {string} fileUrl - The file URL to delete
 * @param {string} fileType - Type of file for logging (e.g., 'main content', 'thumbnail', 'preview')
 * @returns {Promise<boolean>} - Whether deletion was successful
 */
const deleteFile = async (fileUrl, fileType = 'file') => {
  if (!fileUrl) {
    console.log(`[FileCleanup] No ${fileType} URL provided, skipping cleanup`);
    return true;
  }

  console.log(`[FileCleanup] Starting ${fileType} cleanup for: ${fileUrl}`);

  try {
    const isS3File = isS3Url(fileUrl);

    if (isS3File) {
      return await deleteS3File(fileUrl);
    } else {
      return await deleteLocalFile(fileUrl);
    }
  } catch (error) {
    console.error(`[FileCleanup] Error cleaning up ${fileType}:`, error);
    return false;
  }
};

/**
 * Clean up all files associated with a content item
 * @param {Object} content - The content document with file URLs
 * @param {Object} options - Cleanup options
 * @param {boolean} options.skipMainContent - Skip deleting main content file (for soft deletes)
 * @returns {Promise<Object>} - Cleanup results summary
 */
const cleanupContentFiles = async (content, options = {}) => {
  const { skipMainContent = false } = options;

  console.log(`[FileCleanup] Starting comprehensive file cleanup for content: ${content.title} (ID: ${content._id})`);

  const results = {
    mainContent: { attempted: false, success: false, skipped: false },
    thumbnail: { attempted: false, success: false, skipped: false },
    preview: { attempted: false, success: false, skipped: false },
    totalFiles: 0,
    successfulDeletions: 0,
    failedDeletions: 0,
    errors: []
  };

  // Clean up main content file
  if (content.fileUrl && !skipMainContent) {
    results.mainContent.attempted = true;
    results.totalFiles++;

    try {
      const success = await deleteFile(content.fileUrl, 'main content');
      results.mainContent.success = success;
      if (success) {
        results.successfulDeletions++;
      } else {
        results.failedDeletions++;
        results.errors.push(`Failed to delete main content file: ${content.fileUrl}`);
      }
    } catch (error) {
      results.mainContent.success = false;
      results.failedDeletions++;
      results.errors.push(`Error deleting main content file: ${error.message}`);
    }
  } else if (skipMainContent && content.fileUrl) {
    results.mainContent.skipped = true;
    console.log(`[FileCleanup] Skipping main content file deletion (soft delete mode)`);
  }

  // Clean up thumbnail file
  if (content.thumbnailUrl) {
    results.thumbnail.attempted = true;
    results.totalFiles++;

    try {
      const success = await deleteFile(content.thumbnailUrl, 'thumbnail');
      results.thumbnail.success = success;
      if (success) {
        results.successfulDeletions++;
      } else {
        results.failedDeletions++;
        results.errors.push(`Failed to delete thumbnail file: ${content.thumbnailUrl}`);
      }
    } catch (error) {
      results.thumbnail.success = false;
      results.failedDeletions++;
      results.errors.push(`Error deleting thumbnail file: ${error.message}`);
    }
  }

  // Clean up preview file
  if (content.previewUrl) {
    results.preview.attempted = true;
    results.totalFiles++;

    try {
      const success = await deleteFile(content.previewUrl, 'preview');
      results.preview.success = success;
      if (success) {
        results.successfulDeletions++;
      } else {
        results.failedDeletions++;
        results.errors.push(`Failed to delete preview file: ${content.previewUrl}`);
      }
    } catch (error) {
      results.preview.success = false;
      results.failedDeletions++;
      results.errors.push(`Error deleting preview file: ${error.message}`);
    }
  }

  // Log summary
  console.log(`[FileCleanup] Cleanup completed for content: ${content.title}`);
  console.log(`[FileCleanup] Files processed: ${results.totalFiles}, Successful: ${results.successfulDeletions}, Failed: ${results.failedDeletions}`);

  if (results.errors.length > 0) {
    console.warn(`[FileCleanup] Cleanup errors:`, results.errors);
  }

  return results;
};

/**
 * Clean up files for multiple content items (bulk operation)
 * @param {Array} contentItems - Array of content documents
 * @param {Object} options - Cleanup options
 * @returns {Promise<Object>} - Bulk cleanup results summary
 */
const bulkCleanupContentFiles = async (contentItems, options = {}) => {
  console.log(`[FileCleanup] Starting bulk file cleanup for ${contentItems.length} content items`);

  const bulkResults = {
    totalContent: contentItems.length,
    processedContent: 0,
    totalFiles: 0,
    successfulDeletions: 0,
    failedDeletions: 0,
    errors: [],
    contentResults: []
  };

  for (const content of contentItems) {
    try {
      const contentResult = await cleanupContentFiles(content, options);

      bulkResults.processedContent++;
      bulkResults.totalFiles += contentResult.totalFiles;
      bulkResults.successfulDeletions += contentResult.successfulDeletions;
      bulkResults.failedDeletions += contentResult.failedDeletions;
      bulkResults.errors.push(...contentResult.errors);
      bulkResults.contentResults.push({
        contentId: content._id,
        contentTitle: content.title,
        result: contentResult
      });

    } catch (error) {
      bulkResults.errors.push(`Error processing content ${content._id}: ${error.message}`);
      console.error(`[FileCleanup] Error processing content ${content._id}:`, error);
    }
  }

  console.log(`[FileCleanup] Bulk cleanup completed. Processed: ${bulkResults.processedContent}/${bulkResults.totalContent} content items`);
  console.log(`[FileCleanup] Total files: ${bulkResults.totalFiles}, Successful: ${bulkResults.successfulDeletions}, Failed: ${bulkResults.failedDeletions}`);

  return bulkResults;
};

module.exports = {
  deleteFile,
  deleteS3File,
  deleteLocalFile,
  extractS3Key,
  cleanupContentFiles,
  bulkCleanupContentFiles
};
