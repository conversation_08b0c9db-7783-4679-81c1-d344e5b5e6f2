.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content h2 {
  margin: 0 0 1.5rem 0;
  color: var(--secondary-color);
  font-size: var(--heading5);
  font-weight: 600;
}

.modal-content .form-group {
  margin-bottom: 1.5rem;
}

.modal-content .form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  font-weight: 500;
  font-size: var(--basefont);
}

.modal-content .form-group input,
.modal-content .form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  transition: all 0.2s ease;
}

.modal-content .form-group input:focus,
.modal-content .form-group textarea:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

.modal-content .form-group textarea {
  min-height: 120px;
  resize: vertical;
}

.modal-content .rating-input {
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modal-content .rating-input label {
  color: var(--text-color);
  font-weight: 500;
  margin: 0;
  font-size: var(--basefont);
}

.modal-content .modal-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}

.modal-content .right-actions {
  display: flex;
  gap: 1rem;
}

.modal-content .delete-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  background: var(--white);
  border: 1px solid var(--error-color);
  color: var(--error-color);
  transition: all 0.2s ease;
}

.modal-content .delete-button:hover {
  background: var(--error-color);
  color: var(--white);
}

.modal-content .modal-actions button[type="button"]:not(.delete-button) {
  background: transparent;
  border: 1px solid var(--light-gray);
  color: var(--dark-gray);
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-content .modal-actions button[type="button"]:not(.delete-button):hover {
  background: var(--bg-gray);
  border-color: var(--dark-gray);
}

.modal-content .modal-actions button[type="submit"] {
  background: var(--btn-color);
  border: none;
  color: var(--white);
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-content .modal-actions button[type="submit"]:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: scale(1.02);
}

/* Close button */
.modal-content .modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: var(--dark-gray);
  transition: color 0.2s ease;
}

.modal-content .modal-close:hover {
  color: var(--text-color);
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .modal-content {
    width: 95%;
    max-height: 95vh;
    padding: 1.5rem;
  }

  .modal-content .modal-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .modal-content .right-actions {
    width: 100%;
  }

  .modal-content .modal-actions button {
    width: 100%;
  }

  .modal-content .delete-button {
    width: 100%;
    justify-content: center;
  }
}
