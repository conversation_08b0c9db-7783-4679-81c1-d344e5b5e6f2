import { lazy, Suspense, useState, useEffect } from "react";
import { Routes, Route, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";

// Google Analytics
import { trackPageView } from "./utils/GoogleAnalytics";

import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Footer from "../src/components/common/Footer";

// Common Components
import Navbar from "./components/common/Navbar";
import ScrollToTop from "./components/common/ScrollToTop";
import ScrollToTopOnRouteChange from "./components/common/ScrollToTopOnRouteChange";
import Preloader from "./components/common/Preloader";
import ProtectedRoute from "./components/common/ProtectedRoute";
import LaunchStatusGuard from "./components/common/LaunchStatusGuard";

import SettingsProvider from "./components/providers/SettingsProvider";
import { selectIsRestricted } from "./redux/slices/settingsSlice";
import authService from "./services/authService";

// Custom Hooks
import { useFavicon } from "./hooks/useFavicon";
import { useDynamicTitle } from "./hooks/useDynamicTitle";

// Lenis Smooth Scrolling Provider
import LenisProvider from "./utils/LenisProvider";

// Import and preload Lottie animation data
import preloaderAnimation from "./assets/preloader-animation.json";



// Preload the animation data
const preloadedAnimation = { ...preloaderAnimation };

// Lazy-loaded Authentication
const Auth = lazy(() => import("./pages/Authentication/Auth"));
const Signup = lazy(() => import("./pages/Authentication/Signup"));
const OtpVerification = lazy(() =>
  import("./pages/Authentication/OtpVerification")
);

// Lazy-loaded Visitor Pages
const Home = lazy(() => import("./pages/Visitor/Home"));
const Contact = lazy(() => import("./pages/Visitor/Contact"));
// const CheckoutPage = lazy(() => import("./pages/Visitor/CheckoutPage"));
const ThankYou = lazy(() => import("./pages/Visitor/ThankYou"));
const CoachProfilePage = lazy(() => import("./pages/Visitor/CoachProfilePage")); // Added import
const BidThankYou = lazy(() => import("./components/common/BidThankYou")); // Added import for BidThankYou
const RequestThankYou = lazy(() => import("./pages/Visitor/RequestThankYou")); // Added import for RequestThankYou
const StripeOnboardingReturn = lazy(() => import("./pages/Visitor/StripeOnboardingReturn")); // Added import for Stripe onboarding return
const LaunchStatusPage = lazy(() => import("./pages/LaunchStatusPage")); // Added import for Launch Status Page
const NotFound = lazy(() => import("./pages/Visitor/NotFound"));
const CMSPage = lazy(() => import("./pages/CMS/CMSPage"));

// Lazy-loaded Buyer Pages
const BuyerDashboard = lazy(() => import("./pages/Buyer/BuyerDashboard"));
const BuyerContentDetail = lazy(() =>
  import("./pages/Buyer/BuyerContentDetail")
);
const BuyerOrders = lazy(() => import("./pages/Buyer/BuyerOrders"));
const BuyerSettings = lazy(() => import("./pages/Buyer/BuyerSettings"));
const BuyerAccount = lazy(() => import("./pages/Buyer/BuyerAccount"));
const BuyerAccountDashboard = lazy(() =>
  import("./pages/Buyer/BuyerAccountDashboard")
);
//const ItemDetails = lazy(() => import("./pages/Buyer/ItemDetails"));
const DownloadDetails = lazy(() => import("./pages/Buyer/DownloadDetails")); // Added import
const BuyerCheckoutPage = lazy(() => import("./pages/Buyer/CheckoutPage"));
const PaymentSuccessPage = lazy(() =>
  import("./pages/Buyer/PaymentSuccessPage")
);
const BuyerOfferDetails = lazy(() => import("./pages/Buyer/BuyerOfferDetails")); // Added import for offer details

// Lazy-loaded Seller Pages
const SellerDashboard = lazy(() => import("./pages/Seller/SellerDashboard"));
const SellerMyContent = lazy(() => import("./pages/Seller/SellerMyContent"));
const SellerSettings = lazy(() => import("./pages/Seller/SellerSettings"));
const SellerMySportsStrategies = lazy(() =>
  import("./pages/Seller/SellerMySportsStrategies")
);
const AddStrategy = lazy(() => import("./pages/Seller/AddStrategy"));
const EditStrategy = lazy(() => import("./pages/Seller/EditStrategy"));
const StrategyDetails = lazy(() =>
  import("./components/seller/StrategyDetails")
);
const RequestDetails = lazy(() => import("./components/seller/RequestDetails"));
const BidDetails = lazy(() => import("./components/seller/BidDetails"));
const SellerRequests = lazy(() => import("./pages/Seller/SellerRequests"));
const SellerBids = lazy(() => import("./pages/Seller/SellerBids"));
const SellerCards = lazy(() => import("./pages/Seller/SellerCards"));
const SellerOffers = lazy(() => import("./pages/Seller/SellerOffers"));
const OfferDetails = lazy(() => import("./pages/Seller/OfferDetails"));
const SellerProfile = lazy(() => import("./pages/Seller/SellerProfile"));
const SellerOnboarding = lazy(() =>
  import("./components/seller/SellerOnboarding")
);

// Lazy-loaded Admin Pages
const AdminDashboard = lazy(() => import("./pages/Admin/AdminDashboard"));
const AdminUserManagement = lazy(() => import("./pages/Admin/AdminUserManagement"));
const AdminContentManagement = lazy(() => import("./pages/Admin/AdminContentManagement"));
const AdminBidManagement = lazy(() => import("./pages/Admin/AdminBidManagement"));
const AdminOfferManagement = lazy(() => import("./pages/Admin/AdminOfferManagement"));
const AdminOrderManagement = lazy(() => import("./pages/Admin/AdminOrderManagement"));
const AdminRequestManagement = lazy(() => import("./pages/Admin/AdminRequestManagement"));
const AdminReviewManagement = lazy(() => import("./pages/Admin/AdminReviewManagement"));
const AdminSettings = lazy(() => import("./pages/Admin/AdminSettings"));
const AdminCMSPages = lazy(() => import("./pages/Admin/AdminCMSPages"));
const AdminFAQManagement = lazy(() => import("./pages/Admin/AdminFAQManagement"));
const AdminProfile = lazy(() => import("./pages/Admin/AdminProfile"));


const App = () => {
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const [contentLoaded, setContentLoaded] = useState(false);
  const [animationLoaded, setAnimationLoaded] = useState(false);
  // Initialize dynamic favicon and title
  useFavicon();
  useDynamicTitle();



  // Check if current route is an admin route
  const isAdminRoute = location.pathname.startsWith('/admin');

  // Check if site is in restricted mode (countdown/maintenance)
  const isRestricted = useSelector(selectIsRestricted);
  const userData = authService.getStoredUser();
  const isAdmin = userData?.role === 'admin';

  // Check if we're on the countdown page
  const isCountdownPage = location.pathname === '/launch-countdown';

  // Hide header/footer if restricted and not admin, or if on countdown page
  const shouldHideHeaderFooter = (isRestricted && !isAdmin) || isCountdownPage;

  // Check if both animation and content are loaded
  const checkAllLoaded = () => {
    if (animationLoaded && contentLoaded) {
      // Add a small delay for a smooth transition
      setTimeout(() => {
        setIsLoading(false);
      }, 300);
    }
  };

  // Handle animation loaded callback
  const handleAnimationLoaded = () => {
    setAnimationLoaded(true);
    checkAllLoaded();
  };

  // Handle content loaded
  useEffect(() => {
    // Listen for when the page content is fully loaded
    const handleLoad = () => {
      setContentLoaded(true);
      checkAllLoaded();
    };

    // Check if already loaded
    if (document.readyState === "complete") {
      setContentLoaded(true);
      checkAllLoaded();
    } else {
      window.addEventListener("load", handleLoad);
      return () => window.removeEventListener("load", handleLoad);
    }
  }, []);

  // Track page views for Google Analytics
  useEffect(() => {
    // Track the current page view
    trackPageView(location.pathname + location.search, document.title);
  }, [location]);



  return (
    <LenisProvider>
      <SettingsProvider>
        <>
          {/* Preloader */}
          <Preloader
            animationData={preloadedAnimation}
            onLoaded={handleAnimationLoaded}
            isLoading={isLoading}
          />

          {/* Conditionally render Navbar - hide on admin routes and restricted pages */}
          {!isAdminRoute && !shouldHideHeaderFooter && <Navbar />}
          {/* Automatic scroll to top on route change */}
          <ScrollToTopOnRouteChange />
          <main>
            <Suspense>
              <LaunchStatusGuard>
                <Routes>
                  {/* Launch Status Route - accessible during restrictions */}
                  <Route path="/launch-countdown" element={<LaunchStatusPage />} />

                  {/* Visitor Routes */}
                  <Route path="/" element={<Home />} />
                  <Route path="/contact" element={<Contact />} />
                  {/* <Route path="/checkout" element={<CheckoutPage />} /> */}
                  <Route path="/thank-you" element={<ThankYou />} />
                  <Route path="/cms/:slug" element={<CMSPage />} />

                  {/* New Checkout Routes */}
                  <Route
                    path="/checkout/:orderId"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerCheckoutPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/payment-success/:orderId"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <PaymentSuccessPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route path="/coach-profile" element={<CoachProfilePage />} />{" "}
                  {/* Added route */}
                  <Route path="/bid-thank-you" element={<BidThankYou />} />
                  <Route
                    path="/request-thank-you"
                    element={<RequestThankYou />}
                  />{" "}
                  {/* Added route */}
                  <Route
                    path="/stripe-onboarding-return"
                    element={<StripeOnboardingReturn />}
                  />
                  {/* Authentication - Prevent access for logged-in users */}
                  <Route
                    path="/auth"
                    element={
                      <ProtectedRoute preventAuth={true}>
                        <Auth />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/signup"
                    element={
                      <ProtectedRoute preventAuth={true}>
                        <Signup />
                      </ProtectedRoute>
                    }
                  />
                  <Route path="/otp-verification" element={<OtpVerification />} />
                  {/* Buyer Routes */}
                  <Route
                    path="/content"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerDashboard />
                      </ProtectedRoute>
                    }
                  />
                  {/* Update the route to use BuyerContentDetail */}
                  <Route
                    path="/buyer/details/:id"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerContentDetail />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/buyer/orders"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerOrders />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/buyer/settings"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerSettings />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/buyer/download-details/:id"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <DownloadDetails />
                      </ProtectedRoute>
                    }
                  />
                  {/* Buyer Account Routes */}
                  <Route
                    path="/buyer/account/dashboard"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerAccount />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/buyer/account/profile"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerAccount />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/buyer/account/downloads"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerAccount />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/buyer/account/requests"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerAccount />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/buyer/account/bids"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerAccount />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/buyer/account/offers"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerAccount />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/buyer/account/offers/:id"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerOfferDetails />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/buyer/account/cards"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="buyer">
                        <BuyerAccount />
                      </ProtectedRoute>
                    }
                  />
                  {/* Seller Routes */}
                  <Route
                    path="/seller/dashboard"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <SellerDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller-onboarding"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <SellerOnboarding />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/my-sports-strategies"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <SellerMySportsStrategies />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/my-sports-strategies/add"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <AddStrategy />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/strategy-details/:id/edit"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <EditStrategy />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/strategy-details/:id"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <StrategyDetails />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/request-details/:id"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <RequestDetails />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/bid-details/:id"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <BidDetails />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/requests"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <SellerRequests />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/bids"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <SellerBids />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/offers"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <SellerOffers />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/offer-details/:id"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <OfferDetails />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/cards"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <SellerCards />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/payment-settings"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <SellerSettings />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/profile"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <SellerProfile />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/my-content"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <SellerMyContent />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/seller/settings"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="seller">
                        <SellerSettings />
                      </ProtectedRoute>
                    }
                  />

                  {/* Admin Routes */}
                  <Route
                    path="/admin/dashboard"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="admin">
                        <AdminDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/profile"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="admin">
                        <AdminProfile />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/users"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="admin">
                        <AdminUserManagement />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/content"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="admin">
                        <AdminContentManagement />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/bids"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="admin">
                        <AdminBidManagement />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/offers"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="admin">
                        <AdminOfferManagement />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/orders"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="admin">
                        <AdminOrderManagement />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/requests"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="admin">
                        <AdminRequestManagement />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/reviews"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="admin">
                        <AdminReviewManagement />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/cms"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="admin">
                        <AdminCMSPages />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/faqs"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="admin">
                        <AdminFAQManagement />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/settings"
                    element={
                      <ProtectedRoute requireAuth={true} allowedRoles="admin">
                        <AdminSettings />
                      </ProtectedRoute>
                    }
                  />
                  {/* Catch-all route */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </LaunchStatusGuard>
            </Suspense>
          </main>
          {/* Conditionally render Footer - hide on admin routes and restricted pages */}
          {!isAdminRoute && !shouldHideHeaderFooter && <Footer />}
          {/* Scroll to Top Button */}
          <ScrollToTop />

          {/* Toast Container */}
          <ToastContainer
            position="bottom-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
          />
        </>
      </SettingsProvider>
    </LenisProvider>
  );
};

export default App;
