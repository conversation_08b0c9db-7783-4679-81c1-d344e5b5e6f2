import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import { FaTimes, FaGavel, FaExclamationTriangle, FaCheckCircle } from 'react-icons/fa';
import api from '../../services/api';
import { usePlatformCommission, useFinancialSettings } from '../../hooks/useSettings';
import { calculateFeeBreakdown, formatFeeBreakdownForDisplay } from '../../utils/feeCalculations';
import '../../styles/AuctionBidAcceptanceModal.css';
import { formatStandardDateTime } from "../../utils/dateValidation";

const AuctionBidAcceptanceModal = ({
  isOpen,
  onClose,
  bid,
  content,
  onBidAccepted
}) => {
  const dispatch = useDispatch();
  const [isAccepting, setIsAccepting] = useState(false);
  const [sellerResponse, setSellerResponse] = useState('');

  // Use platform commission hook
  const { platformCommission } = usePlatformCommission();

  // Use financial settings hook for Stripe fees
  const {
    loading: isLoadingFinancial,
    stripeProcessingFeePercentage,
    stripeFixedFee
  } = useFinancialSettings();

  const handleAcceptBid = async () => {
    if (!bid || !bid._id) {
      toast.error('Invalid bid data');
      return;
    }

    setIsAccepting(true);

    try {
      const response = await api.put(`/bids/${bid._id}/status`, {
        status: 'accepted',
        sellerResponse: sellerResponse.trim() || 'Bid accepted!'
      });

      if (response.data.success) {
        toast.success('Bid accepted successfully! Auction ended and buyer notified.');

        // Call the callback to refresh bid data
        if (onBidAccepted) {
          onBidAccepted(response.data.data);
        }

        onClose();
      } else {
        throw new Error(response.data.message || 'Failed to accept bid');
      }
    } catch (error) {
      console.error('Error accepting bid:', error);
      toast.error(error.response?.data?.message || 'Failed to accept bid');
    } finally {
      setIsAccepting(false);
    }
  };

  const handleRejectBid = async () => {
    if (!bid || !bid._id) {
      toast.error('Invalid bid data');
      return;
    }

    setIsAccepting(true);

    try {
      const response = await api.put(`/bids/${bid._id}/status`, {
        status: 'rejected',
        sellerResponse: sellerResponse.trim() || 'Bid rejected.'
      });

      if (response.data.success) {
        toast.success('Bid rejected successfully.');

        // Call the callback to refresh bid data
        if (onBidAccepted) {
          onBidAccepted(response.data.data);
        }

        onClose();
      } else {
        throw new Error(response.data.message || 'Failed to reject bid');
      }
    } catch (error) {
      console.error('Error rejecting bid:', error);
      toast.error(error.response?.data?.message || 'Failed to reject bid');
    } finally {
      setIsAccepting(false);
    }
  };

  if (!isOpen || !bid) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="auction-bid-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2 className="modal-title">
            <FaGavel className="modal-icon" />
            Manage Auction Bid
          </h2>
          <button className="modal-close" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        <div className="modal-content">
          {/* Bid Details */}
          <div className="bid-details-section">
            <h3>Bid Details</h3>
            <div className="bid-info-grid">
              <div className="bid-info-item">
                <span className="label">Bidder:</span>
                <span className="value">
                  {bid.bidder?.firstName} {bid.bidder?.lastName}
                </span>
              </div>
              <div className="bid-info-item">
                <span className="label">Bid Amount:</span>
                <span className="value bid-amount">${bid.amount?.toFixed(2)}</span>
              </div>
              <div className="bid-info-item">
                <span className="label">Bid Date:</span>
                <p className="bid-date">
                  Submitted on{' '}
                  {formatStandardDateTime(bid.createdAt)}
                </p>
              </div>
              <div className="bid-info-item">
                <span className="label">Status:</span>
                <span className={`value status-${bid.status?.toLowerCase()}`}>
                  {bid.status}
                </span>
              </div>
            </div>
          </div>

          {/* Content Details */}
          <div className="content-details-section">
            <h3>Content Details</h3>
            <div className="content-info">
              <div className="content-title">{content?.title}</div>
              <div className="content-meta">
                {content?.sport} • {content?.contentType} • {content?.difficulty}
              </div>
            </div>
          </div>

          {/* Warning Section */}
          <div className="warning-section">
            <div className="warning-box">
              <FaExclamationTriangle className="warning-icon" />
              <div className="warning-content">
                <h4>Important Notice</h4>
                <ul>
                  <li>Accepting this bid will <strong>immediately end the auction</strong></li>
                  <li>All other active bids will be marked as "Lost"</li>
                  <li>The content will be <strong>removed from public listing</strong></li>
                  <li>The winning bidder will receive an email with checkout instructions</li>
                  <li>This action <strong>cannot be undone</strong></li>
                </ul>
              </div>
            </div>
          </div>

          {/* Seller Response */}
          <div className="response-section">
            <label htmlFor="sellerResponse" className="response-label">
              Response Message (Optional)
            </label>
            <textarea
              id="sellerResponse"
              className="response-textarea"
              placeholder="Add a personal message to the bidder..."
              value={sellerResponse}
              onChange={(e) => setSellerResponse(e.target.value)}
              maxLength={500}
              rows={3}
            />
            <div className="character-count">
              {sellerResponse.length}/500 characters
            </div>
          </div>

          {/* Earnings Calculation */}
          <div className="earnings-section">
            <h3>Earnings Breakdown</h3>
            {isLoadingFinancial ? (
              <div className="loading-spinner">Loading fee information...</div>
            ) : (() => {
              // Calculate detailed fee breakdown using real settings
              const settings = {
                platformCommissionPercentage: platformCommission,
                stripeProcessingFeePercentage: stripeProcessingFeePercentage,
                stripeFixedFee: stripeFixedFee
              };
              const feeBreakdown = calculateFeeBreakdown(bid.amount, settings);
              const formattedBreakdown = formatFeeBreakdownForDisplay(feeBreakdown);

              return (
                <div className="earnings-grid">
                  <div className="earnings-item">
                    <span className="label">Bid Amount:</span>
                    <span className="value">{formattedBreakdown.originalAmount}</span>
                  </div>
                  <div className="earnings-item deduction">
                    <span className="label">Platform Commission ({feeBreakdown.platformCommissionPercentage}%):</span>
                    <span className="value">-{formattedBreakdown.platformCommission}</span>
                  </div>
                  <div className="earnings-item deduction">
                    <span className="label">Stripe Processing Fee:</span>
                    <span className="value">-{formattedBreakdown.stripeFee}</span>
                  </div>
                  <div className="earnings-item subtotal">
                    <span className="label">Amount After Platform Fee:</span>
                    <span className="value">{formattedBreakdown.sellerEarningsBeforeStripeFees}</span>
                  </div>
                  <div className="earnings-item total">
                    <span className="label">Final Seller Earnings:</span>
                    <span className="value">{formattedBreakdown.finalSellerEarnings}</span>
                  </div>
                  <div className="earnings-summary">
                    <div className="summary-item">
                      <span className="label">Buyer Pays:</span>
                      <span className="value buyer-amount">{formattedBreakdown.buyerPaysAmount}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">Total Fees Deducted:</span>
                      <span className="value fees-amount">{formattedBreakdown.totalFeesDeducted}</span>
                    </div>
                  </div>
                  <div className="earnings-note">
                    <small>
                      <strong>Note:</strong> All fees are deducted from seller earnings.
                      Buyers pay exactly the bid amount with no additional charges.
                    </small>
                  </div>
                </div>
              );
            })()}
          </div>
        </div>

        <div className="modal-actions">
          <button
            className="btn-secondary"
            onClick={onClose}
            disabled={isAccepting}
          >
            Cancel
          </button>

          <button
            className="btn-danger"
            onClick={handleRejectBid}
            disabled={isAccepting}
          >
            {isAccepting ? 'Processing...' : 'Reject Bid'}
          </button>

          <button
            className="btn-success"
            onClick={handleAcceptBid}
            disabled={isAccepting}
          >
            <FaCheckCircle />
            {isAccepting ? 'Accepting...' : 'Accept Bid & End Auction'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AuctionBidAcceptanceModal;
