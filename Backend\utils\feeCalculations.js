/**
 * Simple utility functions for calculating platform fees
 * Note: Stripe automatically deducts their processing fees from transfers
 */

/**
 * Calculate platform fee and transfer amount
 * @param {number} totalAmount - The total transaction amount
 * @param {number} platformFeePercentage - Platform commission percentage (e.g., 5 for 5%)
 * @returns {object} Object containing calculated amounts
 */
const calculatePlatformFee = (totalAmount, platformFeePercentage = 5) => {
  const platformFee = totalAmount * (platformFeePercentage / 100);
  const transferAmount = totalAmount - platformFee;

  return {
    totalAmount: parseFloat(totalAmount.toFixed(2)),
    platformFee: parseFloat(platformFee.toFixed(2)),
    platformFeePercentage,
    transferAmount: parseFloat(transferAmount.toFixed(2)),
    // Note: Actual seller earnings will be less due to Strip<PERSON>'s automatic fee deduction
    note: 'Stripe will automatically deduct processing fees from the transfer amount'
  };
};

/**
 * Calculate fees in cents for Stripe API calls
 * @param {number} totalAmount - The total transaction amount in dollars
 * @param {number} platformFeePercentage - Platform commission percentage
 * @returns {object} Object containing amounts in cents
 */
const calculatePlatformFeeInCents = (totalAmount, platformFeePercentage = 5) => {
  const fees = calculatePlatformFee(totalAmount, platformFeePercentage);

  return {
    totalAmountCents: Math.round(fees.totalAmount * 100),
    platformFeeCents: Math.round(fees.platformFee * 100),
    transferAmountCents: Math.round(fees.transferAmount * 100),
    ...fees // Include dollar amounts as well
  };
};

module.exports = {
  calculatePlatformFee,
  calculatePlatformFeeInCents
};
