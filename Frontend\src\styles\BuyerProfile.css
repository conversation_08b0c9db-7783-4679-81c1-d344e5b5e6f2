.BuyerProfile {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Notification banner for profile photo requirement */
.BuyerProfile .BuyerProfile__notification {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: var(--border-radius);
  color: #856404;
  font-weight: 500;
}

.BuyerProfile .BuyerProfile__notification-icon {
  color: #f39c12;
  font-size: 1.2rem;
}
.BuyerProfile .profile_border_container {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading5);
}
.BuyerProfile .BuyerProfile__container {
  display: flex;
  width: 100%;
  gap: 1rem;
  justify-content: space-between;
}

.BuyerProfile .BuyerProfile__left-section {
  display: grid;

  align-items: start;
}

.BuyerProfile .BuyerProfile__right-section {
  width: 300px;
}

.BuyerProfile .BuyerProfile__form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.BuyerProfile .BuyerProfile__input-field {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.BuyerProfile .BuyerProfile__input-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
}

.BuyerProfile .BuyerProfile__input-container:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.BuyerProfile .BuyerProfile__input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--smallfont);
  color: var(--dark-gray);
}

.BuyerProfile .BuyerProfile__input {
  width: 100%;
  padding: var(--smallfont) var(--basefont);
  border: none;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: transparent;
  outline: none;
}

.BuyerProfile .BuyerProfile__input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.BuyerProfile .BuyerProfile__input--readonly {
  background-color: var(--bg-gray) !important;
  color: var(--dark-gray) !important;
  cursor: not-allowed;
}

.BuyerProfile .BuyerProfile__input--readonly:focus {
  outline: none;
  box-shadow: none;
}

.BuyerProfile .BuyerProfile__textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.BuyerProfile .BuyerProfile__image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--basefont);
  border-radius: var(--border-radius-large);
  border: 1px solid var(--light-gray);
}

.BuyerProfile .BuyerProfile__image-title {
  display: flex;
  justify-content: center;
  font-size: var(--heading6);
  color: var(--secondary-color);
  padding-bottom: var(--basefont);
  font-weight: 500;
}

.BuyerProfile .BuyerProfile__image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid var(--light-gray);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--white);
  margin-bottom: var(--basefont);
}

.BuyerProfile .BuyerProfile__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerProfile .BuyerProfile__placeholder {
  width: 100%;
  height: 100%;
  background-color: var(--bg-gray);
  color: var(--dark-gray);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--heading4);
  font-weight: 600;
}

.BuyerProfile .BuyerProfile__user-icon {
  font-size: 40px;
  color: var(--dark-gray);
}

.BuyerProfile .BuyerProfile__upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
  padding: var(--extrasmallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: var(--smallfont);
}

.BuyerProfile .BuyerProfile__upload-btn:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.BuyerProfile .BuyerProfile__buttons {
  display: flex;
  justify-content: flex-start;
  gap: var(--heading5);
  align-items: center;
  width: 100%;
}

.BuyerProfile .BuyerProfile__save-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--white);
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
  padding: var(--smallfont) var(--heading6);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: var(--basefont);
  min-width: 150px;
}

.BuyerProfile .BuyerProfile__save-btn:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.BuyerProfile .BuyerProfile__delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--dark-gray);
  border: 1px solid var(--light-gray);
  padding: var(--smallfont) var(--heading6);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: var(--basefont);
  min-width: 150px;
}

.BuyerProfile .BuyerProfile__delete-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-color: var(--dark-gray);
  transform: scale(1.02);
}

/* Responsive styles */
@media (max-width: 768px) {
  .BuyerProfile .BuyerProfile__container {
    flex-direction: column;
    align-items: center;
  }

  .BuyerProfile .BuyerProfile__right-section {
    width: 100%;
    margin-bottom: 24px;
    order: -1;
  }

  .BuyerProfile .BuyerProfile__left-section {
    width: 100%;
    gap: 20px;
  }

  .BuyerProfile .BuyerProfile__form-row {
    flex-direction: column;
    gap: 16px;
    display: flex;
  }

  .BuyerProfile .BuyerProfile__buttons {
    flex-direction: column;
    gap: 16px;
  }

  .BuyerProfile .BuyerProfile__save-btn,
  .BuyerProfile .BuyerProfile__delete-btn {
    width: 100%;
  }
}
