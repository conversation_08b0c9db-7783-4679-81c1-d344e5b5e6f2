.bid-thank-you-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
 
 
}

.bid-thank-you-page .bid-thank-you-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 20px;
}

.bid-thank-you-page .bid-thank-you-header-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 20px;
   animation: zoomInOut 2s ease-in-out infinite;
}

.bid-thank-you-page .bid-thank-you-title {
  font-size: var(--heading3);
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: 10px;
}

.bid-thank-you-page .bid-thank-you-subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);

  line-height: 1.5;
}

.bid-thank-you-page .bid-thank-you-card {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  border: 1px solid var(--light-gray);
  padding: 30px;
  width: 100%;
  max-width: 900px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.bid-thank-you-page .bid-thank-you-section {
  display: flex;
  flex-direction: column;
}

.bid-thank-you-page .bid-thank-you-section-title {
  font-size: var(--heading6);
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: 20px;
}

/* Bid Information Grid Layout */
.bid-thank-you-page .bid-information-section .bid-info-grid {
  display: grid;
  grid-template-columns: 1fr 1px 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 10px 30px;
  align-items: start;
}

.bid-thank-you-page .bid-info-grid .bid-thank-you-grid-item:nth-child(1) {
  grid-column: 1;
  grid-row: 1;
}

.bid-thank-you-page .bid-info-grid .bid-thank-you-grid-item:nth-child(2) {
  grid-column: 3;
  grid-row: 1;
}

.bid-thank-you-page .bid-info-grid .bid-thank-you-grid-item:nth-child(3) {
  grid-column: 1;
  grid-row: 2;
}

.bid-thank-you-page .bid-info-grid .bid-thank-you-grid-item:nth-child(4) {
  grid-column: 3;
  grid-row: 2;
}

.bid-thank-you-page .bid-info-divider {
  grid-column: 2;
  grid-row: 1 / -1;
  width: 1px;
  background-color: var(--light-gray);
  justify-self: center;
  height: 100%;
}

.bid-thank-you-page .bid-thank-you-grid-item {
  display: flex;
 justify-content: space-between;
  gap: 5px;

}

.bid-thank-you-page .bid-thank-you-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}
.bid-thank-you-page .bid-thank-you-label::after{
  content: ":";
  margin-left: 4px;
}
.bid-thank-you-page .bid-thank-you-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

.bid-thank-you-page .bid-thank-you-divider {
  border: none;
  border-top: 1px solid var(--light-gray);
  margin: 30px 0;
}

/* Customer and Payment Section */
.bid-thank-you-page .customer-payment-section .customer-payment-flex-container {
  display: flex;
  gap: 40px;
}

.bid-thank-you-page .customer-details-container,
.bid-thank-you-page .payment-details-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.bid-thank-you-page .customer-payment-divider {
  width: 1px;
  background-color: var(--light-gray);
  align-self: stretch;
}

.bid-thank-you-page .bid-thank-you-customer-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.bid-thank-you-page .bid-thank-you-customer-details > div {
  display: flex;
  justify-content: space-between;
  gap: 5px;
}

.bid-thank-you-page .bid-thank-you-payment-details {
  display: flex;
  align-items: center;
  gap: 10px;
}

.bid-thank-you-page .bid-thank-you-payment-icon {
  width: 32px;
  height: auto;
}

/* Item Info Section */
.bid-thank-you-page .bid-thank-you-item-info {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.bid-thank-you-page .bid-thank-you-item-image {
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius-medium);
  object-fit: cover;
  flex-shrink: 0;
}

.bid-thank-you-page .bid-thank-you-item-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
  flex: 1;
}

.bid-thank-you-page .bid-thank-you-item-title {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
  line-height: 1.4;
}

.bid-thank-you-page .bid-thank-you-item-author {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Actions Section */
.bid-thank-you-page .bid-thank-you-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  width: 100%;
  max-width: 900px;
}

.bid-thank-you-page .bid-thank-you-btn-outline,
.bid-thank-you-page .bid-thank-you-btn-primary {
  padding: 15px 30px;
  font-size: var(--basefont);
  font-weight: 500;
  
  cursor: pointer;
  transition: all 0.3s ease;
}


/* Responsive Design */
@media (max-width: 768px) {
  .bid-thank-you-page {
    padding: 30px 15px;
  }

  .bid-thank-you-page .bid-thank-you-card {
    padding: 30px;
  }

  .bid-thank-you-page .bid-information-section .bid-info-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: 10px;
  }

  .bid-thank-you-page .bid-info-grid .bid-thank-you-grid-item:nth-child(1),
  .bid-thank-you-page .bid-info-grid .bid-thank-you-grid-item:nth-child(2),
  .bid-thank-you-page .bid-info-grid .bid-thank-you-grid-item:nth-child(3),
  .bid-thank-you-page .bid-info-grid .bid-thank-you-grid-item:nth-child(4) {
    grid-column: 1;
    grid-row: auto;
  }

  .bid-thank-you-page .bid-info-divider {
    display: none;
  }

  .bid-thank-you-page .customer-payment-section .customer-payment-flex-container {
    flex-direction: column;
    gap: 30px;
  }

  .bid-thank-you-page .customer-payment-divider {
    height: 0.5px;
    width: 100%;
  }

  .bid-thank-you-page .bid-thank-you-actions {
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
  }

  .bid-thank-you-page .bid-thank-you-actions .btn {
    width: 100%;
  }


}

@media (max-width: 480px) {
  .bid-thank-you-page {
    padding: 20px 10px;
  }

  .bid-thank-you-page .bid-thank-you-header-icon {
    width: 50px;
    height: 50px;
  }

  .bid-thank-you-page .bid-thank-you-title {
    font-size: var(--heading5);
  }

  .bid-thank-you-page .bid-thank-you-subtitle {
    font-size: var(--smallfont);
  }

  .bid-thank-you-page .bid-thank-you-card {
    padding: 20px;
  }

  .bid-thank-you-page .bid-thank-you-section-title {
    font-size: var(--basefont);
  }

  .bid-thank-you-page .bid-thank-you-label,
  .bid-thank-you-page .bid-thank-you-value,
  .bid-thank-you-page .bid-thank-you-item-author {
    font-size: var(--smallfont);
  }

  .bid-thank-you-page .bid-thank-you-item-title {
    font-size: var(--basefont);
  }

  .bid-thank-you-page .bid-thank-you-item-image {
    width: 60px;
    height: 60px;
  }
}
@keyframes zoomInOut {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}