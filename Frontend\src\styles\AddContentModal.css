/* AddContentModal Component Styles */
/* .AddContentModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1500; 
  display: flex;
  align-items: center;
  justify-content: center;
}

.AddContentModal .AddContentModal__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.AddContentModal .AddContentModal__container {
  position: relative;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}


.AddContentModal .AddContentModal__header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: var(--white);
  padding: var(--basefont) var(--heading6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.AddContentModal .header-content h2 {
  margin: 0;
  font-size: var(--heading5);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.AddContentModal .close-btn {
  background: none;
  border: none;
  color: var(--white);
  font-size: var(--heading5);
  cursor: pointer;

  border-radius: var(--border-radius);
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.AddContentModal .close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}


.AddContentModal .AddContentModal__content {
  flex: 1;
  overflow-y: auto;
  padding: var(--heading6);
}

.AddContentModal .form-container {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}


.AddContentModal .form-section {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--basefont);
}

.AddContentModal .form-section h3 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: var(--smallfont);
}

.AddContentModal .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
  margin-bottom: var(--basefont);
}

.AddContentModal .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.AddContentModal .form-group label {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

.AddContentModal .form-input,
.AddContentModal .form-textarea,
.AddContentModal .form-select {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-family: inherit;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.AddContentModal .form-input:focus,
.AddContentModal .form-textarea:focus,
.AddContentModal .form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.AddContentModal .form-input.error,
.AddContentModal .form-textarea.error {
  border-color: var(--error-color);
}

.AddContentModal .form-textarea {
  resize: vertical;
  min-height: 100px;
}

.AddContentModal .error-message {
  color: var(--error-color);
  font-size: var(--smallfont);
  margin-top: 4px;
}


.AddContentModal .file-upload-area {
  position: relative;
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--heading6);
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.AddContentModal .file-upload-area:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.AddContentModal .file-upload-area.drag-active {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.AddContentModal .file-upload-area.error {
  border-color: var(--error-color);
}

.AddContentModal .file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.AddContentModal .file-upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--smallfont);
  color: var(--gray);
}

.AddContentModal .file-upload-placeholder svg {
  font-size: var(--heading4);
  color: var(--primary-color);
}

.AddContentModal .file-upload-placeholder p {
  margin: 0;
  font-size: var(--basefont);
  font-weight: 500;
}

.AddContentModal .file-upload-placeholder small {
  font-size: var(--smallfont);
  color: var(--gray);
}

.AddContentModal .file-selected {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  color: var(--success-color);
  font-weight: 500;
}

.AddContentModal .file-selected svg {
  font-size: var(--heading6);
}

.AddContentModal .remove-file {
  background: none;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--border-radius-small);
  transition: background-color 0.3s ease;
}

.AddContentModal .remove-file:hover {
  background-color: rgba(var(--error-color-rgb), 0.1);
}


.AddContentModal .thumbnail-upload {
  position: relative;
  width: 150px;
  height: 100px;
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.AddContentModal .thumbnail-upload:hover {
  border-color: var(--primary-color);
}

.AddContentModal .thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--smallfont);
  color: var(--gray);
  background-color: var(--bg-gray);
}

.AddContentModal .thumbnail-placeholder svg {
  font-size: var(--heading6);
  color: var(--primary-color);
}

.AddContentModal .thumbnail-placeholder span {
  font-size: var(--smallfont);
  font-weight: 500;
}

.AddContentModal .thumbnail-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.AddContentModal .thumbnail-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.AddContentModal .remove-thumbnail {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  border: none;
  color: var(--white);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--border-radius-small);
  font-size: var(--smallfont);
  transition: background-color 0.3s ease;
}

.AddContentModal .remove-thumbnail:hover {
  background-color: var(--error-color);
}


.AddContentModal .AddContentModal__footer {
  background-color: var(--bg-gray);
  padding: var(--basefont) var(--heading6);
  border-top: 1px solid var(--light-gray);
  display: flex;
  justify-content: flex-end;
}

.AddContentModal .footer-actions {
  display: flex;
  gap: var(--basefont);
}


.AddContentModal .btn {
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  text-decoration: none;
  white-space: nowrap;
}

.AddContentModal .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.AddContentModal .btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.AddContentModal .btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.AddContentModal .btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.AddContentModal .btn-outline:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: var(--white);
}


@media (max-width: 768px) {
  .AddContentModal .AddContentModal__container {
    width: 95%;
    max-height: 95vh;
  }

  .AddContentModal .form-row {
    grid-template-columns: 1fr;
  }

  .AddContentModal .footer-actions {
    flex-direction: column;
    width: 100%;
  }

  .AddContentModal .thumbnail-upload {
    width: 100%;
    height: 120px;
  }
} */
