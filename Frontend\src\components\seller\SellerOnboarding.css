.seller-onboarding-wrapper {
  width: 100%;
  margin: 1rem auto;
  background: var(--white);
  border-radius: var(--border-radius-large);

  padding: 2rem 1.5rem 1.5rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.seller-onboarding-wrapper .seller-onboarding-step2-container {
  display: grid;
  justify-items: center;
  gap: 2rem;
  align-items: center;
  width: 100%;
}

.seller-onboarding-wrapper .progress-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  width: 50%;
}
.seller-onboarding-wrapper .step {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--light-gray);
  color: var(--dark-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--heading6);
  border: 2px solid var(--light-gray);
  transition: background 0.3s, color 0.3s;
}
.seller-onboarding-wrapper .step.active {
  background: var(--btn-color);
  color: var(--white);
  border: 2px solid var(--btn-color);
}
.seller-onboarding-wrapper .step.complete {
  background: var(--primary-color);
  color: var(--white);
  border: 2px solid var(--primary-color);
}
.seller-onboarding-wrapper .progress-line {
  flex: 1 1 60px;
  height: 2px;
  background: var(--light-gray);
  margin: 0 0.5rem;
}

.seller-onboarding-wrapper .section-block {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 1.5rem 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
  width: 100%;
}
.seller-onboarding-wrapper .section-title {
  font-size: var(--heading6);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 0.5rem;
  position: absolute;
  top: -20px;
  left: 50%;
  width: max-content;
  background: var(--white);
  padding: 0 0.5rem;
  transform: translate(-50%, 10px);
}
.seller-onboarding-wrapper .min-cost-input {
  width: 100%;
  min-width: 0;
  padding: 0.5rem 0.75rem;
  font-size: var(--basefont);
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.seller-onboarding-wrapper .social-inputs-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.seller-onboarding-wrapper .social-input-row {
  display: flex;
  align-items: center;

  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}
.seller-onboarding-wrapper .social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--white);
}
.seller-onboarding-wrapper .social-icon.facebook svg {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 18px;
  height: 18px;
}
.seller-onboarding-wrapper .social-icon.instagram svg {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
}
.seller-onboarding-wrapper .social-icon.twitter svg {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 18px;
  height: 18px;
}
.seller-onboarding-wrapper .social-input {
  flex: 1 1 200px;
  min-width: 0;
  padding: 0.5rem 0.75rem;
  font-size: var(--basefont);
  background: var(--white);
  border-top: none;
  border-right: none;
  border-bottom: none;
  border-left: 1px solid var(--light-gray);
  color: #0000ee;
}

.seller-onboarding-wrapper .next-btn-row {
  display: flex;
  justify-content: center;
  gap: 1rem;
}
.seller-onboarding-wrapper .next-btn {
  min-width: 160px;
  font-size: var(--basefont);
  border-radius: var(--border-radius-large);
}

.seller-onboarding-wrapper .error-message {
  background: var(--error-color);
  color: var(--white);
  padding: 1rem;
  border-radius: var(--border-radius);
  text-align: center;
  margin-top: 1rem;
  font-size: var(--basefont);
}

/* Inline field error styles */
.seller-onboarding-wrapper .field-error {
  color: var(--error-color);
  font-size: var(--smallfont);
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Error state for input fields */
.seller-onboarding-wrapper .input.error,
.seller-onboarding-wrapper .description-textarea.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
}

.seller-onboarding-wrapper .input.error:focus,
.seller-onboarding-wrapper .description-textarea.error:focus {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
  outline: none;
}

/* Submission progress styles */
.seller-onboarding-wrapper .submission-progress {
  background: var(--bg-blue);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 1.5rem;
  margin: 1rem 0;
  text-align: center;
}

.seller-onboarding-wrapper .progress-message {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.seller-onboarding-wrapper .progress-steps {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.seller-onboarding-wrapper .progress-step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--smallfont);
  color: var(--dark-gray);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  background: var(--white);
  border: 1px solid var(--light-gray);
  transition: all 0.3s ease;
}

.seller-onboarding-wrapper .progress-step.active {
  background: var(--primary-light-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
  font-weight: 600;
}

.seller-onboarding-wrapper .progress-step.completed {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
  font-weight: 600;
}

/* Server error message styles */
.seller-onboarding-wrapper .server-error-message {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  padding: var(--basefont);
  margin: var(--basefont) 0;
  color: #dc2626;
}

.seller-onboarding-wrapper .error-icon {
  font-size: var(--basefont);
  flex-shrink: 0;
}

.seller-onboarding-wrapper .error-text {
  font-size: var(--smallfont);
  font-weight: 500;
  line-height: 1.4;
}
.seller-onboarding-wrapper .seller-onboarding-step3-container {
  display: grid;
  justify-items: center;
  width: 100%;
  align-items: center;
  gap: 2rem;
}
.seller-onboarding-wrapper.stripe-connect-info ul {
  list-style: none;
}
.seller-onboarding-wrapper .stripe-connect-setup,
.seller-onboarding-wrapper .stripe-connect-info {
  display: grid;
  gap: 1rem;
}
@media (max-width: 900px) {
  .seller-onboarding-wrapper {
    padding: 1rem 0.5rem;
  }
}
@media (max-width: 600px) {
  .seller-onboarding-wrapper {
    padding: 0.5rem 0.8rem;
  }
  .seller-onboarding-wrapper .section-block {
    padding: 1rem 0.5rem;
  }
  .seller-onboarding-wrapper .next-btn {
    min-width: 120px;
    font-size: var(--smallfont);
  }
  .seller-onboarding-wrapper .progress-bar {
    width: 100%;
  }
}
/* Required field indicator */
.seller-onboarding-wrapper .required-field::after {
  content: "*";
  color: #dc3545;
  margin-left: 4px;
}

/* Optional field indicator */
.seller-onboarding-wrapper .optional-field::after {
  content: "(optional)";
  color: var(--dark-gray);
  font-size: 0.85em;
  margin-left: 4px;
  font-weight: normal;
}

/* Stripe Connect Button Highlight */
.seller-onboarding-wrapper .btn-stripe-connect {
  position: relative;
  background: linear-gradient(45deg, #6772e5, #4b5ce4);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
  box-shadow: 0 4px 12px rgba(103, 114, 229, 0.3);
}

.seller-onboarding-wrapper .btn-stripe-connect:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(103, 114, 229, 0.4);
  background: linear-gradient(45deg, #7883e8, #5c6ce7);
}

.seller-onboarding-wrapper .btn-stripe-connect:disabled {
  animation: none;
  background: #e0e0e0;
  cursor: not-allowed;
  box-shadow: none;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(103, 114, 229, 0.3);
  }
  50% {
    box-shadow: 0 4px 24px rgba(103, 114, 229, 0.5);
  }
  100% {
    box-shadow: 0 4px 12px rgba(103, 114, 229, 0.3);
  }
}

/* Complete Onboarding Button - Disabled State */
.seller-onboarding-wrapper .next-btn-row .btn-primary:disabled {
  background-color: #e0e0e0;
  color: #ffffff;
  cursor: not-allowed;
  border: none;
  opacity: 0.7;
}

.seller-onboarding-wrapper .next-btn-row .btn-primary:not(:disabled) {
  background: var(--primary-color);
  color: white;
  border: none;
  transition: all 0.3s ease;
  opacity: 1;
}

.seller-onboarding-wrapper .next-btn-row .btn-primary:not(:disabled):hover {
  transform: translateY(-2px);
}

/* Action Required Message */
.seller-onboarding-wrapper .action-required {
  margin-top: 16px;
  padding: 12px;
  background-color: #fff3e0;
  border-left: 4px solid #ff9800;
  border-radius: 4px;
  color: #e65100;
  font-weight: 600;
  font-size: 14px;
}

/* Button Note */
.seller-onboarding-wrapper .button-note {
  font-size: 12px;
  opacity: 0.9;
  margin-top: 4px;
  font-weight: normal;
}

.seller-onboarding-wrapper .lock-icon {
  display: inline-block;
  margin-right: 8px;
  animation: wiggle 1s ease-in-out infinite;
}

@keyframes wiggle {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-10deg);
  }
  75% {
    transform: rotate(10deg);
  }
}

/* Stripe Connect Container */
.seller-onboarding-wrapper .stripe-connect-container {
  display: flex;
  flex-direction: column;
  gap: 24px;


  border-radius: 8px;
  margin-top: 16px;
}

.seller-onboarding-wrapper .stripe-connect-info {
  background-color: white;
  padding: 20px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.seller-onboarding-wrapper .stripe-connect-info h4 {
  color: #2d3748;
  margin-bottom: 16px;
}

.seller-onboarding-wrapper .stripe-connect-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.seller-onboarding-wrapper .stripe-connect-info li {
  margin-bottom: 12px;
  color: #4a5568;
  display: flex;
  align-items: center;
  gap: 8px;
}
