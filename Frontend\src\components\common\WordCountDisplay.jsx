// import React from 'react';
// import { countWords, validateWordCount } from '../../utils/textValidation';
// import './WordCountDisplay.css';

// /**
//  * Word Count Display Component
//  * Shows word count, character count, and validation status
//  */
// const WordCountDisplay = ({
//   text = '',
//   maxWords = null,
//   maxChars = null,
//   showCharCount = false,
//   className = '',
//   style = {}
// }) => {
//   const validation = validateWordCount(text, maxWords);
//   const charCount = text ? text.replace(/<[^>]*>/g, '').length : 0;

//   const getStatusClass = () => {
//     // If no limits, always normal status
//     if (!maxWords && !maxChars) return 'word-count-normal';

//     if (!validation.isValid) return 'word-count-error';
//     if (maxWords && validation.remaining <= 50) return 'word-count-warning';
//     return 'word-count-normal';
//   };

//   return (
//     <div className={`word-count-display ${getStatusClass()} ${className}`} style={style}>
//       <div className="word-count-info">
//         <span className="word-count-text">
//           <strong>{validation.wordCount}</strong>
//           {maxWords ? `/${maxWords} words` : ' words'}
//         </span>

//         {showCharCount && (
//           <span className="char-count-text">
//             <strong>{charCount}</strong>
//             {maxChars ? `/${maxChars} characters` : ' characters'}
//           </span>
//         )}

//         {maxWords && !validation.isValid && (
//           <span className="word-count-error-text">
//             Exceeds limit by {Math.abs(validation.remaining)} words
//           </span>
//         )}

//         {maxWords && validation.isValid && validation.remaining <= 50 && validation.remaining > 0 && (
//           <span className="word-count-warning-text">
//             {validation.remaining} words remaining
//           </span>
//         )}

//         {!maxWords && !maxChars && (
//           <span className="word-count-unlimited-text">
//             No limits - unlimited text
//           </span>
//         )}
//       </div>

//       {maxWords && (
//         <div className="word-count-bar">
//           <div
//             className="word-count-progress"
//             style={{
//               width: `${Math.min((validation.wordCount / maxWords) * 100, 100)}%`
//             }}
//           />
//         </div>
//       )}
//     </div>
//   );
// };

// export default WordCountDisplay;
