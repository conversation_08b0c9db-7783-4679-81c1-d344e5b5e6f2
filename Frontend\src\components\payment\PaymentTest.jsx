// import { useState } from 'react';
// import StripePaymentForm from './StripePaymentForm';

// /**
//  * Test component for Stripe payment integration
//  * This component can be used to test the payment flow in development
//  */
// const PaymentTest = () => {
//   const [showPaymentForm, setShowPaymentForm] = useState(false);

//   // Mock order data for testing
//   const mockOrder = {
//     _id: 'test-order-123',
//     amount: 22.00,
//     content: {
//       title: 'Test Sports Strategy',
//       coachName: 'Test Coach',
//       contentType: 'Video',
//       thumbnailUrl: 'https://via.placeholder.com/100x100'
//     },
//     buyer: 'test-buyer-id',
//     seller: 'test-seller-id',
//     orderType: 'Fixed',
//     status: 'Pending',
//     paymentStatus: 'Pending'
//   };

//   const handlePaymentSuccess = (result) => {
//     alert('Payment successful! Check console for details.');
//     setShowPaymentForm(false);
//   };

//   const handlePaymentError = (error) => {
//     console.error('Payment error:', error);
//     alert(`Payment failed: ${error.message}`);
//   };

//   const handlePaymentCancel = () => {
//     setShowPaymentForm(false);
//   };

//   return (
//     <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
//       <h2>Stripe Payment Integration Test</h2>

//       <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ddd', borderRadius: '8px' }}>
//         <h3>Test Order Details</h3>
//         <p><strong>Order ID:</strong> {mockOrder._id}</p>
//         <p><strong>Content:</strong> {mockOrder.content.title}</p>
//         <p><strong>Coach:</strong> {mockOrder.content.coachName}</p>
//         <p><strong>Amount:</strong> ${mockOrder.amount.toFixed(2)}</p>
//         <p><strong>Status:</strong> {mockOrder.status}</p>
//       </div>

//       {!showPaymentForm ? (
//         <div>
//           <button
//             onClick={() => setShowPaymentForm(true)}
//             style={{
//               padding: '12px 24px',
//               backgroundColor: '#3b82f6',
//               color: 'white',
//               border: 'none',
//               borderRadius: '8px',
//               cursor: 'pointer',
//               fontSize: '16px'
//             }}
//           >
//             Test Payment Flow
//           </button>

//           <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f9fafb', borderRadius: '8px' }}>
//             <h4>Test Instructions:</h4>
//             <ul>
//               <li>Click "Test Payment Flow" to open the payment form</li>
//               <li>Use test card: <code>4242 4242 4242 4242</code></li>
//               <li>Use any future expiry date (e.g., 12/25)</li>
//               <li>Use any 3-digit CVC (e.g., 123)</li>
//               <li>Enter any name for the cardholder</li>
//             </ul>

//             <h4>Test Cards:</h4>
//             <ul>
//               <li><strong>Success:</strong> 4242 4242 4242 4242</li>
//               <li><strong>Decline:</strong> 4000 0000 0000 0002</li>
//               <li><strong>Insufficient Funds:</strong> 4000 0000 0000 9995</li>
//             </ul>
//           </div>
//         </div>
//       ) : (
//         <div>
//           <h3>Payment Form</h3>
//           <StripePaymentForm
//             order={mockOrder}
//             onSuccess={handlePaymentSuccess}
//             onError={handlePaymentError}
//             onCancel={handlePaymentCancel}
//           />
//         </div>
//       )}
//     </div>
//   );
// };

// export default PaymentTest;
