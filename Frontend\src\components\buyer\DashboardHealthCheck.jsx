// import React, { useEffect, useState } from 'react';
// import { useSelector, useDispatch } from 'react-redux';
// import {
//   selectLoading,
//   selectErrors,
//   selectDashboardStats,
//   selectMyDownloads,
//   selectMyRequests,
//   selectMyBids,
//   selectAllStrategies,
//   selectNotifications,
//   fetchBuyerDashboardStats,
//   fetchBuyerDownloads,
//   fetchBuyerRequests,
//   fetchBuyerBids,
//   fetchAllStrategies,
//   fetchBuyerNotifications,
// } from '../../redux/slices/buyerDashboardSlice';
// import { FaCheckCircle, FaExclamationTriangle, FaSpinner } from 'react-icons/fa';

// const DashboardHealthCheck = () => {
//   const dispatch = useDispatch();
//   const [isVisible, setIsVisible] = useState(false);
  
//   const loading = useSelector(selectLoading);
//   const errors = useSelector(selectErrors);
//   const stats = useSelector(selectDashboardStats);
//   const downloads = useSelector(selectMyDownloads);
//   const requests = useSelector(selectMyRequests);
//   const bids = useSelector(selectMyBids);
//   const strategies = useSelector(selectAllStrategies);
//   const notifications = useSelector(selectNotifications);

//   // Only show in development mode
//   useEffect(() => {
//     setIsVisible(process.env.NODE_ENV === 'development');
//   }, []);

//   const runHealthCheck = () => {
//     dispatch(fetchBuyerDashboardStats());
//     dispatch(fetchBuyerDownloads({ limit: 5 }));
//     dispatch(fetchBuyerRequests({ limit: 5 }));
//     dispatch(fetchBuyerBids({ limit: 5 }));
//     dispatch(fetchAllStrategies({ limit: 8 }));
//     dispatch(fetchBuyerNotifications());
//   };

//   const getStatusIcon = (section) => {
//     if (loading[section]) return <FaSpinner className="spin" />;
//     if (errors[section]) return <FaExclamationTriangle className="error" />;
//     return <FaCheckCircle className="success" />;
//   };

//   const getStatusText = (section) => {
//     if (loading[section]) return 'Loading...';
//     if (errors[section]) return `Error: ${errors[section]}`;
//     return 'OK';
//   };

//   const healthChecks = [
//     { key: 'stats', label: 'Dashboard Stats', data: stats },
//     { key: 'downloads', label: 'Downloads', data: downloads },
//     { key: 'requests', label: 'Requests', data: requests },
//     { key: 'bids', label: 'Bids', data: bids },
//     { key: 'strategies', label: 'Strategies', data: strategies },
//     { key: 'notifications', label: 'Notifications', data: notifications },
//   ];

//   if (!isVisible) return null;

//   return (
//     <div className="dashboard-health-check">
//       <div className="health-check-header">
//         <h4>Dashboard Health Check</h4>
//         <button onClick={runHealthCheck} className="refresh-btn">
//           Refresh All
//         </button>
//       </div>
      
//       <div className="health-check-list">
//         {healthChecks.map(({ key, label, data }) => (
//           <div key={key} className="health-check-item">
//             <div className="status-icon">
//               {getStatusIcon(key)}
//             </div>
//             <div className="check-info">
//               <span className="check-label">{label}</span>
//               <span className="check-status">{getStatusText(key)}</span>
//               {data && (
//                 <span className="data-count">
//                   ({Array.isArray(data) ? data.length : 'loaded'} items)
//                 </span>
//               )}
//             </div>
//           </div>
//         ))}
//       </div>

//       <style jsx>{`
//         .dashboard-health-check {
//           position: fixed;
//           bottom: 20px;
//           right: 20px;
//           background: white;
//           border: 1px solid #ddd;
//           border-radius: 8px;
//           padding: 16px;
//           box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
//           max-width: 300px;
//           z-index: 1000;
//           font-size: 12px;
//         }

//         .health-check-header {
//           display: flex;
//           justify-content: space-between;
//           align-items: center;
//           margin-bottom: 12px;
//           padding-bottom: 8px;
//           border-bottom: 1px solid #eee;
//         }

//         .health-check-header h4 {
//           margin: 0;
//           font-size: 14px;
//           color: #333;
//         }

//         .refresh-btn {
//           background: #007bff;
//           color: white;
//           border: none;
//           padding: 4px 8px;
//           border-radius: 4px;
//           font-size: 10px;
//           cursor: pointer;
//         }

//         .health-check-list {
//           display: flex;
//           flex-direction: column;
//           gap: 8px;
//         }

//         .health-check-item {
//           display: flex;
//           align-items: center;
//           gap: 8px;
//         }

//         .status-icon {
//           width: 16px;
//           display: flex;
//           align-items: center;
//           justify-content: center;
//         }

//         .status-icon .success {
//           color: #28a745;
//         }

//         .status-icon .error {
//           color: #dc3545;
//         }

//         .status-icon .spin {
//           color: #007bff;
//           animation: spin 1s linear infinite;
//         }

//         @keyframes spin {
//           from { transform: rotate(0deg); }
//           to { transform: rotate(360deg); }
//         }

//         .check-info {
//           display: flex;
//           flex-direction: column;
//           gap: 2px;
//         }

//         .check-label {
//           font-weight: 500;
//           color: #333;
//         }

//         .check-status {
//           color: #666;
//           font-size: 10px;
//         }

//         .data-count {
//           color: #999;
//           font-size: 10px;
//         }
//       `}</style>
//     </div>
//   );
// };

// export default DashboardHealthCheck;
