/* Countdown Timer Styles */
.countdown-timer {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* margin: 16px 0; */
}

.countdown-timer .countdown-prefix {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 12px;
  text-align: center;
}

.countdown-timer .countdown-display {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 16px;
  background-color: var(--primary-light-color);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
}

.countdown-timer .countdown-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-width: 60px;
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
  background-color: var(--white);
  box-shadow: var(--box-shadow-light);
  padding: 8px;
}

.countdown-timer .countdown-number {
  font-size: var(--heading5);
  font-weight: 700;
  color: var(--secondary-color);
  line-height: 1;
  margin-bottom: 2px;
}

.countdown-timer .countdown-label {
  font-size: 9px;
  font-weight: 600;
  color: var(--dark-gray);
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* Size Variants */
.countdown-timer.small .countdown-display {
  gap: 8px;
  padding: 12px;
}

.countdown-timer.small .countdown-item {
  min-width: 45px;
  width: 45px;
  height: 45px;
  padding: 6px;
}



.countdown-timer.small .countdown-label {
  font-size: 8px;
}

.countdown-timer.large .countdown-display {
  gap: 20px;
  padding: 24px;
}

.countdown-timer.large .countdown-item {
  min-width: 80px;
  width: 80px;
  height: 80px;
  padding: 12px;
}

.countdown-timer.large .countdown-number {
  font-size: var(--heading3);
}

.countdown-timer.large .countdown-label {
  font-size: 11px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .countdown-timer .countdown-display {
    gap: 8px;
    padding: 12px;
  }

  .countdown-timer .countdown-item {
    min-width: 50px;
    width: 50px;
    height: 50px;
    padding: 6px;
  }

  .countdown-timer .countdown-number {
    font-size: var(--smallfont);
  }

  .countdown-timer .countdown-label {
    font-size: 8px;
  }

  .countdown-timer.large .countdown-item {
    min-width: 60px;
    width: 60px;
    height: 60px;
  }

  .countdown-timer.large .countdown-number {
    font-size: var(--heading5);
  }
}

@media (max-width: 480px) {
  .countdown-timer .countdown-display {
    gap: 6px;
    padding: 8px;
  }

  .countdown-timer .countdown-item {
    min-width: 40px;
    width: 40px;
    height: 40px;
    padding: 4px;
  }

  .countdown-timer .countdown-number {
    font-size: var(--extrasmallfont);
  }

  .countdown-timer .countdown-label {
    font-size: 7px;
  }

  .countdown-timer .countdown-prefix {
    font-size: var(--extrasmallfont);
    margin-bottom: 8px;
  }
}
@media (max-width: 300px) {
 .countdown-timer.small .countdown-display {
  gap: 8px;
  padding: 0px;
  border: none;
}
  }