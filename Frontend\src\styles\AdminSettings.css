/* AdminSettings Component Styles */
.AdminSettings {
  background-color: var(--bg-gray);
  min-height: calc(100vh - 70px);
}

/* Header Section */
.AdminSettings .AdminSettings__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--light-gray);
}

.AdminSettings .AdminSettings__header .header-left h1 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.AdminSettings .AdminSettings__header .header-icon {
  color: var(--primary-color);
  font-size: 1.8rem;
}

.AdminSettings .AdminSettings__header .header-left p {
  color: var(--dark-gray);
  margin: 0;
  font-size: 1rem;
}

.AdminSettings .AdminSettings__header .header-right {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Loading State */
.AdminSettings .AdminSettings__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.AdminSettings .AdminSettings__loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--light-gray);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.AdminSettings .AdminSettings__loading p {
  color: var(--dark-gray);
  font-size: 1.1rem;
}

/* Form Styles */
.AdminSettings .AdminSettings__form {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

/* Settings Section */
.AdminSettings .settings-section {
  background: var(--white);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--light-gray);
}

.AdminSettings .section-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--light-gray);
}

.AdminSettings .section-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.AdminSettings .section-header .section-icon {
  color: var(--dark-gray);
  font-size: 1.3rem;
}

.AdminSettings .section-header p {
  color: var(--dark-gray);
  margin: 0;
  font-size: 0.95rem;
}

/* Form Grid */
.AdminSettings .form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  align-items: start;
}

/* Form Section Title */
.AdminSettings .form-section-title {
  margin: 2rem 0 1.5rem 0;
  padding: 1rem 0;
  border-top: 1px solid var(--light-gray);
}

.AdminSettings .form-section-title h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.AdminSettings .form-section-title p {
  color: var(--dark-gray);
  margin: 0;
  font-size: 0.9rem;
}

.AdminSettings .form-group {
  display: flex;
  flex-direction: column;
}

.AdminSettings .form-group.full-width {
  grid-column: 1 / -1;
}

.AdminSettings .form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.AdminSettings .form-group label svg {
  color: var(--dark-gray);
  font-size: 1rem;
}

.AdminSettings .form-control {
  padding: 0.75rem 1rem;
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background-color: var(--white);
}

.AdminSettings .form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.AdminSettings .form-control::placeholder {
  color: var(--dark-gray);
}

.AdminSettings textarea.form-control {
  resize: vertical;
  min-height: 80px;
}

.AdminSettings .form-text {
  font-size: 0.85rem;
  color: var(--dark-gray);
  margin-top: 0.25rem;
  max-width: fit-content;
}

/* Toggle Button */
.AdminSettings .toggle-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.AdminSettings .toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  background: var(--white);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
  width: fit-content;
}

.AdminSettings .toggle-btn:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.05);
}

.AdminSettings .toggle-btn.active {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.1);
}

.AdminSettings .toggle-btn .toggle-on {
  color: #10b981;
  font-size: 1.2rem;
}

.AdminSettings .toggle-btn .toggle-off {
  color: var(--dark-gray);
  font-size: 1.2rem;
}

/* Button Styles */
.AdminSettings .btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
}

.AdminSettings .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.AdminSettings .btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.AdminSettings .btn-primary:hover:not(:disabled) {
  background-color: #d63c2a;
  transform: translateY(-1px);
}

.AdminSettings .btn-secondary {
  background-color: var(--btn-color) ;
  color: var(--white) !important;
  border: 1px solid var(--light-gray);
}

.AdminSettings .btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-gray);
  border-color: var(--primary-color);
}

/* Logo Upload Styles */
.AdminSettings .logo-upload-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.AdminSettings .current-logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.AdminSettings .logo-preview {
  max-width: 200px;
  max-height: 100px;
  border-radius: 8px;
  border: 1px solid var(--light-gray);
  object-fit: contain;
  background-color: var(--white);
  padding: 0.5rem;
}

.AdminSettings .favicon-preview {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid var(--light-gray);
  object-fit: contain;
  background-color: var(--white);
  padding: 0.25rem;
}

.AdminSettings .file-input {
  cursor: pointer;
}

.AdminSettings .file-input:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.AdminSettings .upload-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  font-size: 0.9rem;
}

.AdminSettings .upload-progress .loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--light-gray);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Error State */
.AdminSettings .AdminSettings__error {
  background-color: #fef2f2;
  border: 1px solid var(--error-color);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.AdminSettings .AdminSettings__error p {
  color: var(--error-color);
  margin: 0;
  font-weight: 500;
}

/* Responsive Design */
/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
  .AdminSettings .form-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }

  .AdminSettings .settings-section {
    padding: 2.5rem;
  }
}

/* Desktop/Tablet Landscape (1024px - 1199px) */
@media (max-width: 1199px) and (min-width: 1024px) {
  .AdminSettings .form-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.25rem;
  }

  .AdminSettings .settings-section {
    padding: 2rem;
  }

  .AdminSettings .AdminSettings__header .header-left h1 {
    font-size: 1.75rem;
  }

  .AdminSettings .section-header h2 {
    font-size: 1.4rem;
  }
}

/* Tablet Portrait (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .AdminSettings .AdminSettings__header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .AdminSettings .AdminSettings__header .header-right {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .AdminSettings .form-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .AdminSettings .settings-section {
    padding: 1.75rem;
  }

  .AdminSettings .AdminSettings__header .header-left h1 {
    font-size: 1.6rem;
  }

  .AdminSettings .section-header h2 {
    font-size: 1.3rem;
  }

  .AdminSettings .btn {
    min-height: 44px; /* Touch target requirement */
    padding: 0.75rem 1.25rem;
  }
}

/* Mobile Landscape/Large Mobile (481px - 767px) */
@media (max-width: 767px) and (min-width: 481px) {
  /* Ensure all form controls fit within screen */
  .AdminSettings .form-control,
  .AdminSettings input,
  .AdminSettings textarea,
  .AdminSettings select {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
  .AdminSettings .AdminSettings__header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .AdminSettings .AdminSettings__header .header-right {
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .AdminSettings .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .AdminSettings .settings-section {
    padding: 1.5rem;
  }

  .AdminSettings .AdminSettings__header .header-left h1 {
    font-size: 1.5rem;
  }

  .AdminSettings .section-header h2 {
    font-size: 1.25rem;
  }

  .AdminSettings .btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: var(--smallfont);
  }

  /* Optimize social media inputs for landscape mobile */
  .AdminSettings .social-input-group .form-control {
    padding-left: 2.5rem;
  }

  .AdminSettings .social-icon-wrapper {
    left: 0.75rem;
  }
}

/* Mobile Portrait (320px - 480px) */
@media (max-width: 480px) {
  /* Ensure all form controls fit within screen */
  .AdminSettings .form-control,
  .AdminSettings input,
  .AdminSettings textarea,
  .AdminSettings select {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
  .AdminSettings .AdminSettings__header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .AdminSettings .AdminSettings__header .header-right {
    flex-direction: column;
    gap: 0.75rem;
  }

  .AdminSettings .btn {
    width: 100%;
    justify-content: center;
    min-height: 44px;
    padding: 0.875rem 1rem;
    font-size: var(--smallfont);
  }

  .AdminSettings .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .AdminSettings .settings-section {
    padding: 1rem 0.75rem;
    margin: 0;
    border-radius: 8px;
  }

  .AdminSettings .AdminSettings__header .header-left h1 {
    font-size: 1.4rem;
  }

  .AdminSettings .section-header h2 {
    font-size: 1.2rem;
  }

  .AdminSettings .section-header {
    padding-bottom: 0.75rem;
    margin-bottom: 1.5rem;
  }

  /* Form optimizations for mobile */
  .AdminSettings .form-control {
    padding: 0.875rem 0.75rem;
    font-size: var(--basefont);
    min-height: 44px;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .AdminSettings textarea.form-control {
    min-height: 100px;
    resize: vertical;
  }

  /* Logo upload optimizations */
  .AdminSettings .logo-upload-container {
    gap: 0.75rem;
  }

  .AdminSettings .logo-preview {
    max-width: 150px;
    max-height: 75px;
  }

  .AdminSettings .favicon-preview {
    width: 28px;
    height: 28px;
  }

  /* Social media section mobile optimization */
  .AdminSettings .social-media-section {
    padding: 1.25rem;
    margin-top: 0.75rem;
  }

  .AdminSettings .social-input-group .form-control {
    padding-left: 2.25rem;
    font-size: var(--smallfont);
  }

  .AdminSettings .social-icon-wrapper {
    left: 0.625rem;
    width: 18px;
    height: 18px;
  }

  .AdminSettings .social-icon-wrapper svg {
    font-size: 1rem;
  }

  /* Toggle button optimizations */
  .AdminSettings .toggle-button {
    padding: 0.75rem 0.875rem;
    font-size: var(--smallfont);
    min-height: 44px;
  }

  .AdminSettings .toggle-button svg {
    font-size: 1.25rem;
  }

  /* Status indicators mobile optimization */
  .AdminSettings .status-indicator {
    padding: 0.75rem 0.875rem;
    font-size: var(--smallfont);
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .AdminSettings .status-indicator svg {
    font-size: 1.1rem;
  }

  /* Launch status info mobile */
  .AdminSettings .launch-status-info {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
  }

  .AdminSettings .status-card {
    padding: 1.25rem;
  }

  .AdminSettings .status-card h4 {
    font-size: var(--heading6);
    margin-bottom: 0.75rem;
  }
}

/* Extra Small Mobile (320px - 360px) */
@media (max-width: 360px) {
  .AdminSettings {
    padding: 0;
  }

  .AdminSettings .AdminSettings__header {
    gap: 0.5rem;
    padding: 0 0.5rem;
  }

  .AdminSettings .settings-section {
    padding: 0.75rem 0.5rem;
    margin: 0;
    border-radius: 6px;
  }

  .AdminSettings .section-header {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
  }

  .AdminSettings .AdminSettings__header .header-left h1 {
    font-size: 1.25rem;
  }

  .AdminSettings .section-header h2 {
    font-size: 1.1rem;
  }

  .AdminSettings .form-control {
    padding: 0.75rem 0.625rem;
    font-size: var(--smallfont);
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .AdminSettings .btn {
    padding: 0.75rem 0.875rem;
    font-size: var(--extrasmallfont);
    min-height: 42px;
  }

  .AdminSettings .social-media-section {
    padding: 1rem;
  }

  .AdminSettings .social-input-group .form-control {
    padding-left: 2rem;
    font-size: var(--extrasmallfont);
  }

  .AdminSettings .social-icon-wrapper {
    left: 0.5rem;
    width: 16px;
    height: 16px;
  }

  .AdminSettings .social-icon-wrapper svg {
    font-size: 0.875rem;
  }

  .AdminSettings .toggle-button {
    padding: 0.625rem 0.75rem;
    font-size: var(--extrasmallfont);
    min-height: 42px;
  }

  .AdminSettings .toggle-button svg {
    font-size: 1.1rem;
  }

  .AdminSettings .status-indicator {
    padding: 0.625rem 0.75rem;
    font-size: var(--extrasmallfont);
  }

  .AdminSettings .status-indicator svg {
    font-size: 1rem;
  }

  .AdminSettings .logo-preview {
    max-width: 120px;
    max-height: 60px;
  }

  .AdminSettings .favicon-preview {
    width: 24px;
    height: 24px;
  }
}

/* Social Media Section Styles */
.AdminSettings .social-media-section {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 1px solid #e1e8ff;
  border-radius: 12px;
  padding: 2rem;
  margin-top: 1rem;
}

.AdminSettings .social-media-section .form-section-title {
  margin-top: 0;
  padding-top: 0;
  border-top: none;
}

.AdminSettings .social-media-section .form-section-title h3 {
  color: #4f46e5;
}

.AdminSettings .social-input-group {
  position: relative;
}

.AdminSettings .social-input-group .form-control {
  padding-left: 3rem;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.AdminSettings .social-input-group .form-control:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.AdminSettings .social-icon-wrapper {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.AdminSettings .social-icon-wrapper svg {
  font-size: 1.1rem;
}

.AdminSettings .social-icon-wrapper.facebook svg {
  color: #1877f2;
}

.AdminSettings .social-icon-wrapper.twitter svg {
  color: #1da1f2;
}

.AdminSettings .social-icon-wrapper.instagram svg {
  color: #e4405f;
}

/* Upload Section Improvements */
.AdminSettings .upload-section {
  background: #fafbfc;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.AdminSettings .upload-section:hover {
  border-color: #4f46e5;
  background: #f8f9ff;
}

.AdminSettings .upload-section.has-file {
  border-style: solid;
  border-color: #10b981;
  background: #f0fdf4;
}

.AdminSettings .upload-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.AdminSettings .upload-icon {
  width: 48px;
  height: 48px;
  background: #e5e7eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 1.5rem;
}

.AdminSettings .upload-text {
  color: #374151;
  font-weight: 500;
}

.AdminSettings .upload-subtext {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Launch Settings Styles */
.AdminSettings .toggle-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.AdminSettings .toggle-wrapper {
  margin-bottom: 0.5rem;
}

.AdminSettings .toggle-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: var(--white);
  border: 2px solid var(--light-gray);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--dark-gray);
}

.AdminSettings .toggle-button:hover {
  border-color: var(--primary-color);
  background: var(--primary-light-color);
}

.AdminSettings .toggle-button.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

.AdminSettings .toggle-button svg {
  font-size: 1.5rem;
}

.AdminSettings .launch-status-info {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--light-gray);
}

.AdminSettings .status-card {
  background: var(--white);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--light-gray);
}

.AdminSettings .status-card h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: var(--heading6);
  font-weight: 600;
}

.AdminSettings .status-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  font-weight: 500;
}

.AdminSettings .status-indicator.live {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.AdminSettings .status-indicator.countdown {
  background: rgba(251, 191, 36, 0.1);
  color: #d97706;
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.AdminSettings .status-indicator.maintenance {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.AdminSettings .status-indicator svg {
  font-size: 1.2rem;
}

/* Additional Mobile Optimizations */

/* Horizontal scrolling for tables/content on mobile */
@media (max-width: 767px) {
  .AdminSettings .table-container,
  .AdminSettings .overflow-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--light-gray) transparent;
  }

  .AdminSettings .table-container::-webkit-scrollbar {
    height: 6px;
  }

  .AdminSettings .table-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .AdminSettings .table-container::-webkit-scrollbar-thumb {
    background-color: var(--light-gray);
    border-radius: 3px;
  }

  /* Form text and help text optimization */
  .AdminSettings .form-text {
    font-size: var(--extrasmallfont);
    line-height: 1.4;
    margin-top: 0.375rem;
  }

  /* Upload section mobile optimization */
  .AdminSettings .upload-section {
    padding: 1rem;
    border-width: 1px;
  }

  .AdminSettings .upload-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }

  .AdminSettings .upload-text {
    font-size: var(--smallfont);
  }

  .AdminSettings .upload-subtext {
    font-size: var(--extrasmallfont);
  }

  /* Loading state mobile optimization */
  .AdminSettings .AdminSettings__loading {
    padding: 3rem 1rem;
  }

  .AdminSettings .AdminSettings__loading .loading-spinner {
    width: 32px;
    height: 32px;
    border-width: 2px;
  }

  .AdminSettings .AdminSettings__loading p {
    font-size: var(--basefont);
  }

  /* Upload progress mobile */
  .AdminSettings .upload-progress {
    font-size: var(--extrasmallfont);
  }

  .AdminSettings .upload-progress .loading-spinner {
    width: 14px;
    height: 14px;
    border-width: 1px;
  }
}

/* Accessibility and Focus Improvements for Mobile */
@media (max-width: 767px) {
  /* Enhanced focus states for touch devices */
  .AdminSettings .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  .AdminSettings .btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);
  }

  .AdminSettings .toggle-button:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);
  }

  .AdminSettings .file-input:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }

  /* Improved tap targets */
  .AdminSettings .form-group label {
    padding: 0.25rem 0;
    cursor: pointer;
  }

  .AdminSettings .toggle-label {
    padding: 0.25rem 0;
    cursor: pointer;
  }

  /* Better spacing for touch interaction */
  .AdminSettings .form-group {
    margin-bottom: 1.25rem;
  }

  .AdminSettings .form-group:last-child {
    margin-bottom: 0;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .AdminSettings .form-control {
    border-width: 2px;
  }

  .AdminSettings .btn {
    border-width: 2px;
    border-style: solid;
  }

  .AdminSettings .btn-primary {
    border-color: var(--primary-color);
  }

  .AdminSettings .btn-secondary {
    border-color: var(--text-color);
  }

  .AdminSettings .toggle-button {
    border-width: 2px;
  }

  .AdminSettings .status-indicator {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .AdminSettings .btn,
  .AdminSettings .form-control,
  .AdminSettings .toggle-button,
  .AdminSettings .settings-section,
  .AdminSettings .upload-section {
    transition: none;
  }

  .AdminSettings .loading-spinner {
    animation: none;
  }

  .AdminSettings .loading-spinner::after {
    content: "Loading...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: var(--extrasmallfont);
    color: var(--primary-color);
  }
}

/* Mobile Navigation and Layout Improvements */
@media (max-width: 767px) {
  /* Ensure proper spacing from mobile navigation */
  .AdminSettings {
    padding-top: 0.5rem;
  }

  /* Sticky header for mobile */
  .AdminSettings .AdminSettings__header {
    position: sticky;
    top: 0;
    background-color: var(--bg-gray);
    z-index: 10;
    padding: 1rem 0.5rem;
    margin: -0.5rem -0.5rem 1rem -0.5rem;
    border-bottom: 1px solid var(--light-gray);
  }

  /* Form section spacing optimization */
  .AdminSettings .AdminSettings__form {
    gap: 1.5rem;
  }

  /* Better error message display on mobile */
  .AdminSettings .AdminSettings__error {
    margin: 0 -0.5rem;
    border-radius: 6px;
    padding: 0.875rem;
    font-size: var(--smallfont);
  }

  /* Optimize datetime input for mobile */
  .AdminSettings input[type="datetime-local"] {
    font-size: 16px; /* Prevents zoom on iOS */
    -webkit-appearance: none;
    appearance: none;
  }

  /* Optimize number input for mobile */
  .AdminSettings input[type="number"] {
    font-size: 16px; /* Prevents zoom on iOS */
    -webkit-appearance: none;
    appearance: none;
  }

  /* Better file input styling for mobile */
  .AdminSettings .file-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.875rem 0.75rem;
  }

  /* Mobile-optimized form validation styles */
  .AdminSettings .form-control:invalid {
    border-color: var(--error-color);
    box-shadow: none;
  }

  .AdminSettings .form-control:valid {
    border-color: var(--light-gray);
  }
}

/* Landscape orientation optimizations */
@media (max-width: 767px) and (orientation: landscape) {
  .AdminSettings .AdminSettings__header {
    position: relative;
    padding: 0.75rem 0.5rem;
    margin-bottom: 1rem;
  }

  .AdminSettings .settings-section {
    padding: 1.25rem;
  }

  .AdminSettings .form-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
  }

  .AdminSettings .btn {
    min-height: 40px;
    padding: 0.625rem 1rem;
  }
}

/* Print styles for admin settings */
@media print {
  .AdminSettings .AdminSettings__header .header-right,
  .AdminSettings .btn,
  .AdminSettings .file-input,
  .AdminSettings .upload-section {
    display: none !important;
  }

  .AdminSettings .settings-section {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
    margin-bottom: 1rem;
  }

  .AdminSettings .form-control {
    border: 1px solid #000;
    background: transparent;
  }

  .AdminSettings {
    background: white;
  }
}
