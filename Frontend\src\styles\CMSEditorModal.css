/* CMSEditorModal Component Styles */
.CMSEditorModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1500; /* Above navbar (1000) and sidebar (200) */
  display: flex;
  align-items: center;
  justify-content: center;
}

.CMSEditorModal__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.CMSEditorModal__container {
  position: relative;
  width: 95%;
  max-width: 1200px;
  height: 90vh;
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header */
.CMSEditorModal__header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: var(--white);
  padding: var(--basefont) var(--heading6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.CMSEditorModal .header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  margin-right: var(--basefont);
}

.CMSEditorModal .header-content h2 {
  margin: 0;
  font-size: var(--heading5);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.CMSEditorModal .header-actions {
  display: flex;
  gap: var(--smallfont);
}

.CMSEditorModal .close-btn {
  background: none;
  border: none;
  color: var(--white);
  font-size: var(--heading5);
  cursor: pointer;
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.CMSEditorModal .close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Content */
.CMSEditorModal__content {
  flex: 1;
  overflow-y: auto;
  padding: var(--heading6);
}

/* Editor Form */
.CMSEditorModal .editor-form {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

.CMSEditorModal .form-section {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--basefont);
}

.CMSEditorModal.form-section h3 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: var(--smallfont);
}

.CMSEditorModal .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.CMSEditorModal.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.CMSEditorModal .form-group label {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

.CMSEditorModal .form-input,
.CMSEditorModal .form-textarea,
.CMSEditorModal .form-select {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-family: inherit;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.CMSEditorModal .form-input:focus,
.CMSEditorModal .form-textarea:focus,
.CMSEditorModal .form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.CMSEditorModal .form-input.error,
.CMSEditorModal .form-textarea.error {
  border-color: var(--error-color);
}

.CMSEditorModal .form-textarea {
  resize: vertical;
  min-height: 80px;
}

.CMSEditorModal .content-editor {
  min-height: 300px;
  font-family: "Courier New", monospace;
  line-height: 1.6;
}

.CMSEditorModal .error-message {
  color: var(--error-color);
  font-size: var(--smallfont);
  margin-top: 4px;
}

.CMSEditorModal .char-count {
  color: var(--gray);
  font-size: var(--smallfont);
  text-align: right;
  margin-top: 4px;
}

/* Editor Toolbar */
.CMSEditorModal .editor-toolbar {
  display: flex;
  gap: var(--basefont);
  padding: var(--smallfont);
  background-color: var(--bg-gray);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  margin-bottom: var(--smallfont);
}

.CMSEditorModal .toolbar-group {
  display: flex;
  gap: 4px;
  padding-right: var(--basefont);
  border-right: 1px solid var(--light-gray);
}

.CMSEditorModal .toolbar-group:last-child {
  border-right: none;
  padding-right: 0;
}

.CMSEditorModal .toolbar-group button {
  background: none;
  border: none;
  padding: 6px 8px;
  border-radius: var(--border-radius-small);
  cursor: pointer;
  color: var(--text-color);
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.CMSEditorModal .toolbar-group button:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

/* Preview Content */
.CMSEditorModal .preview-content {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--heading6);
  background-color: var(--white);
  min-height: 500px;
}

.CMSEditorModal .preview-header {
  margin-bottom: var(--heading6);
  padding-bottom: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.CMSEditorModal .preview-header h1 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading3);
  color: var(--text-color);
}

.CMSEditorModal .featured-image {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: var(--border-radius);
  margin-top: var(--basefont);
}

.CMSEditorModal .preview-body {
  line-height: 1.8;
  color: var(--text-color);
  font-size: var(--basefont);
}

/* Footer */
.CMSEditorModal__footer {
  background-color: var(--secondary-color);
  padding: var(--basefont) var(--heading6);
  border-top: 1px solid var(--light-gray);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.CMSEditorModal .footer-info {
  color: var(--white);
  font-size: var(--smallfont);
}

.CMSEditorModal .footer-actions {
  display: flex;
  gap: var(--basefont);
}

/* Buttons */
.CMSEditorModal .btn {
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  text-decoration: none;
  white-space: nowrap;
}

.CMSEditorModal .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.CMSEditorModal .btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.CMSEditorModal .btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.CMSEditorModal .btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.CMSEditorModal .btn-outline:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: var(--white);
}



/* Summernote Editor Styles */
.CMSEditorModal .cms-summernote {
  margin-bottom: var(--basefont-sm);
  overflow: auto;
}

.CMSEditorModal .cms-summernote .note-editor {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.CMSEditorModal .cms-summernote .note-editor.note-frame {
  border: 1px solid var(--light-gray);
   border-radius: var(--border-radius);
}

.CMSEditorModal .cms-summernote .note-editor.note-frame:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.CMSEditorModal .cms-summernote .note-toolbar {
  background-color: var(--background-light);
  border-bottom: 1px solid var(--light-gray);
}

.CMSEditorModal .cms-summernote .note-editable {
  background-color: var(--white);
  color: var(--text-color);
  font-size: var(--basefont);
  line-height: 1.6;
  padding: var(--basefont);
}

.CMSEditorModal .cms-summernote .note-editable:focus {
  background-color: var(--white);
}

/* Summernote Modal and Dropdown Responsive Styles */
.CMSEditorModal .cms-summernote .note-modal {
  z-index: 2000;
}

.CMSEditorModal .cms-summernote .note-modal .modal-dialog {
  margin: 10px;
  max-width: calc(100vw - 20px);
}

.CMSEditorModal .cms-summernote .note-dropdown-menu {
  max-width: 200px;
  font-size: var(--smallfont);
}

.CMSEditorModal .cms-summernote .note-color-palette {
  width: auto;
  max-width: 200px;
}

.CMSEditorModal .cms-summernote .note-color-palette .note-color-btn {
  width: 20px;
  height: 20px;
  margin: 1px;
}

/* Summernote Fullscreen Mode */
.CMSEditorModal .cms-summernote .note-editor.fullscreen {
  z-index: 2500;
}

.CMSEditorModal .cms-summernote .note-editor.fullscreen .note-editable {
  padding: var(--basefont);
}

/* Validation Error Styles */
.CMSEditorModal .validation-error {
  margin-top: var(--basefont-xs);
}

.CMSEditorModal .validation-error .error-message {
  color: var(--error-color);
  font-size: var(--basefont-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--basefont-xs);
}

.CMSEditorModal .validation-error .error-message::before {
  content: "⚠";
  color: var(--error-color);
}

.CMSEditorModal .error-message {
  color: var(--error-color);
  font-size: var(--basefont-sm);
  margin-top: var(--basefont-xs);
  display: block;
}

.CMSEditorModal .char-count {
  color: var(--text-secondary);
  font-size: var(--basefont-sm);
  margin-top: var(--basefont-xs);
  display: block;
}

.CMSEditorModal .meta-description {
  color: var(--text-secondary);
  font-style: italic;
  margin-bottom: var(--basefont);
}

/* Slug field styling */
.CMSEditorModal .slug-toggle-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.8rem;
  margin-left: 0.5rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  transition: background-color 0.2s ease;
}

.CMSEditorModal .slug-toggle-btn:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.CMSEditorModal .auto-generated {
  background-color: var(--background-light);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.CMSEditorModal .help-text {
  display: block;
  color: var(--btn-color);
  font-size: var(--basefont-xs);
  margin-top: 0.25rem;
  font-style: italic;
}

.CMSEditorModal .input-with-button {
  position: relative;
  display: flex;
  align-items: center;
}

.CMSEditorModal .input-with-button .form-input {
  flex: 1;
  padding-right: 2.5rem;
}

.CMSEditorModal .reset-slug-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--border-radius);
  transition: color 0.2s ease, background-color 0.2s ease;
}

.CMSEditorModal .reset-slug-btn:hover {
  color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.1);
}
.CMSEditorModal .form-group {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 5px;
  flex-wrap: wrap;
  margin-bottom: 15px;
  overflow: auto;
  width: 100%;
}

/* Content Editor Responsive Adjustments */
.CMSEditorModal .content-editor {
  min-height: 300px;
  font-family: "Courier New", monospace;
  line-height: 1.6;
  overflow-x: auto;
}

/* Form Input Responsive Improvements */
.CMSEditorModal .form-input,
.CMSEditorModal .form-textarea,
.CMSEditorModal .form-select {
  width: 100%;
  box-sizing: border-box;
}

/* Preview Content Responsive */
.CMSEditorModal .preview-content {
  overflow-x: auto;
}

.CMSEditorModal .preview-content img {
  max-width: 100%;
  height: auto;
}

.CMSEditorModal .preview-content table {
  width: 100%;
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

.CMSEditorModal .preview-content pre {
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
/* Responsive Design */
@media (max-width: 1024px) {
  .CMSEditorModal__container {
    width: 96%;
    height: 92vh;
  }

  .CMSEditorModal__content {
    padding: var(--basefont);
  }

  /* Summernote Editor Responsive Adjustments */
  .CMSEditorModal .cms-summernote .note-toolbar {
    padding: var(--smallfont);
  }

  .CMSEditorModal .cms-summernote .note-toolbar .note-btn-group {
    margin-right: var(--smallfont);
    margin-bottom: var(--smallfont);
  }

  .CMSEditorModal .cms-summernote .note-editable {
    padding: var(--smallfont);
    font-size: var(--basefont);
  }
}

@media (max-width: 768px) {
  .CMSEditorModal {
    padding: 15px;
  }

  .CMSEditorModal__container {
    width: 95%;
    max-height: 95vh;
    margin: 0;
  }

  .CMSEditorModal__header {
    padding: var(--smallfont) var(--basefont);
    align-items: flex-start;
  }

  .CMSEditorModal .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
    margin-right: var(--smallfont);
  }

  .CMSEditorModal .header-content h2 {
    font-size: var(--heading6);
  }

  .CMSEditorModal .header-actions {
    align-self: stretch;
  }

  .CMSEditorModal .header-actions .btn {
    font-size: var(--smallfont);
    padding: var(--smallfont);
  }

  .CMSEditorModal__content {
    padding: var(--smallfont);
  }

  .CMSEditorModal .form-section {
    padding: var(--smallfont);
  }

  .CMSEditorModal .form-row {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }

  /* Rich Text Editor Mobile Optimizations */
  .CMSEditorModal .cms-summernote {
    margin-bottom: var(--smallfont);
  }

  .CMSEditorModal .cms-summernote .note-editor {
    border-radius: var(--border-radius-small);
  }

  .CMSEditorModal .cms-summernote .note-toolbar {
    padding: 4px;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: thin;
  }

  .CMSEditorModal .cms-summernote .note-toolbar::-webkit-scrollbar {
    height: 4px;
  }

  .CMSEditorModal .cms-summernote .note-toolbar::-webkit-scrollbar-track {
    background: var(--light-gray);
  }

  .CMSEditorModal .cms-summernote .note-toolbar::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 2px;
  }

  .CMSEditorModal .cms-summernote .note-btn-group {
    margin-right: 4px;
    margin-bottom: 4px;
    display: inline-flex;
    flex-shrink: 0;
  }

  .CMSEditorModal .cms-summernote .note-btn {
    padding: 4px 6px;
    font-size: 12px;
    min-width: 28px;
    height: 28px;
    border-radius: var(--border-radius-small);
  }

  .CMSEditorModal .cms-summernote .note-editable {
    padding: var(--smallfont);
    font-size: var(--smallfont);
    line-height: 1.5;
    min-height: 200px;
  }

  .CMSEditorModal .cms-summernote .note-statusbar {
    padding: 4px var(--smallfont);
    font-size: var(--smallfont);
  }

  .CMSEditorModal .editor-toolbar {
    flex-wrap: wrap;
    gap: var(--smallfont);
    padding: var(--smallfont);
  }

  .CMSEditorModal .toolbar-group {
    border-right: none;
    padding-right: 0;
    margin-bottom: var(--smallfont);
  }

  .CMSEditorModal .footer-actions {
    flex-direction: column;
    width: 100%;
    gap: var(--smallfont);
  }

  .CMSEditorModal .footer-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .CMSEditorModal__footer {
    flex-direction: column;
    gap: var(--smallfont);
    align-items: stretch;
    padding: var(--smallfont) var(--basefont);
  }

  .CMSEditorModal .footer-info {
    text-align: center;
    margin-bottom: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .CMSEditorModal {
    padding: 10px;
  }

  .CMSEditorModal__container {
    width: 98%;
    max-height: 98vh;
    border-radius: var(--border-radius);
  }

  .CMSEditorModal__header {
    padding: var(--smallfont);
  }

  .CMSEditorModal .header-content h2 {
    font-size: var(--basefont);
  }

  .CMSEditorModal .close-btn {
    width: 32px;
    height: 32px;
    font-size: var(--basefont);
  }

  .CMSEditorModal__content {
    padding: var(--smallfont);
  }

  .CMSEditorModal .form-section h3 {
    font-size: var(--basefont);
    margin-bottom: var(--smallfont);
  }

  /* Ultra-compact rich text editor for very small screens */
  .CMSEditorModal .cms-summernote .note-toolbar {
    padding: 2px;
  }

  .CMSEditorModal .cms-summernote .note-btn {
    padding: 2px 4px;
    font-size: 10px;
    min-width: 24px;
    height: 24px;
  }

  .CMSEditorModal .cms-summernote .note-editable {
    padding: 8px;
    font-size: 14px;
    min-height: 150px;
  }

  .CMSEditorModal .form-input,
  .CMSEditorModal .form-textarea,
  .CMSEditorModal .form-select {
    padding: 8px;
    font-size: 14px;
  }

  .CMSEditorModal .btn {
    padding: 8px 12px;
    font-size: 14px;
  }
}

@media (max-width: 350px) {
  .CMSEditorModal__header {
    align-items: flex-start;
    padding: 8px;
  }

  .CMSEditorModal .header-content {
    flex-direction: column;
    gap: 4px;
  }

  .CMSEditorModal .header-content h2 {
    font-size: 14px;
  }

  .CMSEditorModal .close-btn {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  /* Minimal rich text editor for extremely small screens */
  .CMSEditorModal .cms-summernote .note-toolbar {
    flex-wrap: wrap;
    max-height: 60px;
    overflow-y: auto;
  }

  .CMSEditorModal .cms-summernote .note-btn-group {
    margin: 1px;
  }

  .CMSEditorModal .cms-summernote .note-btn {
    padding: 1px 3px;
    font-size: 9px;
    min-width: 20px;
    height: 20px;
  }

  .CMSEditorModal .cms-summernote .note-editable {
    padding: 6px;
    font-size: 13px;
    min-height: 120px;
  }
}