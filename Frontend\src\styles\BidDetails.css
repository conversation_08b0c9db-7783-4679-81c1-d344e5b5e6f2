/* BidDetails Component Styles */
.BidDetails {
  padding: 0;
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
  color: var(--text-color);
}

.BidDetails .BidDetails__content {
  display: flex;
  flex-direction: column;
  gap: var(--heading4);
}

/* Header Section */
.BidDetails .BidDetails__header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  margin-bottom: var(--heading4);
}

.BidDetails .BidDetails__content-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.BidDetails .BidDetails__content-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.BidDetails .BidDetails__content-details {
  flex: 1;
}

.BidDetails .BidDetails__content-title {
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
  margin: 0 0 var(--extrasmallfont) 0;
  line-height: 1.4;
}

.BidDetails .BidDetails__content-subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
  margin: 0;
}

/* Main Section */

.BidDetails .BidDetails__info-grid {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading5);
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--heading4);
  margin-top: var(--heading4);
}

.BidDetails .BidDetails__info-section {
  display: flex;
  justify-content: space-around;
  gap: var(--basefont);
}

.BidDetails .BidDetails__section-title {
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: var(--smallfont);
}

.BidDetails .vertical-line {
  width: 1px;
  background-color: var(--light-gray);
  height: 100%;
}

.BidDetails .BidDetails__info-item-grid {
  width: 100%;
}

.BidDetails .bidDetails-btn-grid {
  display: flex;
  align-items: center;
}

.BidDetails .BidDetails__info-item {
  display: flex;
  gap: 5px;
  align-items: center;
  padding: var(--smallfont) 0;
  border-bottom: 1px solid #f5f5f5;
}

.BidDetails .BidDetails__info-label {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
}
.BidDetails .BidDetails__info-label::after {
  content: ":";
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
  margin-left: 4px;
}
.BidDetails .BidDetails__info-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
}

/* Action Buttons */
.BidDetails .BidDetails__actions {
  padding: 0 0 0 var(--heading4);
  display: flex;

  gap: var(--smallfont);
}

.BidDetails .BidDetails__btn {
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: fit-content;
}

.BidDetails .BidDetails__btn--accept {
  background-color: var(--second-primary-color);
  color: white;
}

.BidDetails .BidDetails__btn--accept:hover {
  transform: scale(1.02);
}

.BidDetails .BidDetails__btn--reject {
  background-color: #dc3545;
  color: white;
}

.BidDetails .BidDetails__btn--reject:hover {
  background-color: #c82333;
}

/* History Section */
.BidDetails .BidDetails__history-section {
  background-color: var(--white);
}



/* Status Badges */
.BidDetails .status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--smallfont);
  font-weight: 500;

  display: inline-block;
}

.BidDetails .status-outbid {
  background-color: #fff3cd;
  color: #856404;
}

.BidDetails .status-accepted {
  background-color: #d4edda;
  color: #155724;
}
.BidDetails .status-won {
  background-color: #d4edda;
  color: #155724;
}
.BidDetails .status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.BidDetails .status-counter-offer {
  background-color: #d1ecf1;
  color: #0c5460;
}

/* Error State */
.BidDetails .BidDetails__error {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--heading3);
  color: var(--dark-gray);
  font-size: var(--heading6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .BidDetails .BidDetails__info-item {
    
  }

  .BidDetails .BidDetails__info-grid {
    grid-template-columns: 1fr;
    gap: var(--heading5);
    overflow: auto;
  }

  .BidDetails .BidDetails__content-info {
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .BidDetails .BidDetails__content-image {
    width: 100%;
  }

  .BidDetails .BidDetails__btn {
    text-align: center;
  }

  .BidDetails .BidDetails__section-title {
    font-size: var(--basefont);
  }

  .BidDetails .BidDetails__content-title {
    font-size: var(--basefont);
  }

  .BidDetails .BidDetails__content-subtitle,
  .BidDetails .BidDetails__info-label,
  .BidDetails .BidDetails__info-value {
    font-size: var(--smallfont);
  }
  .BidDetails .BidDetails__info-section {
    flex-direction: column;
    gap: 10px;
  }
  .BidDetails .vertical-line {
    display: none;
  }
}

@media (max-width: 480px) {
  .BidDetails .BidDetails__info-item {
    
    gap: 0px;
    padding: 2px;
    border-bottom: none;
  }

  .BidDetails .BidDetails__section-title {
    font-size: var(--smallfont);
    margin-bottom: var(--extrasmallfont);
    margin-top: var(--basefont);
  }

  .BidDetails .BidDetails__content-title {
    font-size: var(--smallfont);
  }

  .BidDetails .BidDetails__content-subtitle,
  .BidDetails .BidDetails__info-label,
  .BidDetails .BidDetails__info-value {
    font-size: var(--extrasmallfont);
  }

  .BidDetails .BidDetails__btn {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--extrasmallfont);
  }

  .BidDetails .status-badge {
    font-size: var(--extrasmallfont);
    padding: 2px 6px;
    min-width: 60px;
  }
}

