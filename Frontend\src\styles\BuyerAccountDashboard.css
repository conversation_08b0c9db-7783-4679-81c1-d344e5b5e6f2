.BuyerAccountDashboard {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Stats Cards */
.BuyerAccountDashboard .stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--basefont);
  margin-bottom: var(--heading6);
}

.BuyerAccountDashboard .stat-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: var(--basefont);
  border-radius: var(--border-radius);
  color: var(--white);
  text-align: center;
}

.BuyerAccountDashboard .stat-card.downloads {
  background-image: url("../assets/images/buyerdashboardone.svg");
  object-fit: cover;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: #f3e8ff;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border-radius: var(--border-radius-large);
}
.BuyerAccountDashboard .stat-card:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.BuyerAccountDashboard .icon-round {
  border-radius: 50%;
  background-color: #bf83ff;
  padding: 10px;
  font-size: var(--heading5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}
.BuyerAccountDashboard .stat-card.requests {
  background-image: url("../assets/images/buyerdashboardtwo.svg");
  object-fit: cover;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: #fff2da;
  display: flex;
  align-items: center;
  align-content: space-around;
  flex-direction: row;
  justify-content: space-between;
  border-radius: var(--border-radius-large);
}
.BuyerAccountDashboard .icon-roundtwo {
  border-radius: 50%;
  background-color: #ff947a;
  padding: 10px;
  font-size: var(--heading5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}
.BuyerAccountDashboard .stat-card.bids {
  background-image: url("../assets/images/buyerdashboardthree.svg");
  object-fit: cover;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: #dcfce7;
  display: flex;
  align-items: center;
  align-content: space-around;
  flex-direction: row;
  justify-content: space-between;
  border-radius: var(--border-radius-large);
}
.BuyerAccountDashboard .icon-roundthree {
  border-radius: 50%;
  background-color: #3cd856;
  padding: 10px;
  font-size: var(--heading5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}

.BuyerAccountDashboard .stat-card.offers {
  background-image: url("../assets/images/buyerdashboardfour.svg");
  object-fit: cover;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: #e0f2fe;
  display: flex;
  align-items: center;
  align-content: space-around;
  flex-direction: row;
  justify-content: space-between;
  border-radius: var(--border-radius-large);
}

.BuyerAccountDashboard .icon-roundfour {
  border-radius: 50%;
  background-color: #73a0b1;
  padding: 10px;
  font-size: var(--heading5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}

.BuyerAccountDashboard .stat-number {
  font-size: var(--heading5);
  font-weight: 700;

  color: var(--secondary-color);
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: flex-start;
}

.BuyerAccountDashboard .stat-label {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--secondary-color);
}

/* Section Styles */
.BuyerAccountDashboard .section {
  margin-bottom: var(--heading6);
  padding: var(--heading5);
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius);
}

.BuyerAccountDashboard .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--basefont);
}

.BuyerAccountDashboard .section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.BuyerAccountDashboard .retry-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--btn-color);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.BuyerAccountDashboard .retry-btn:hover {
  background-color: var(--primary-color);
  transform: scale(1.1);
}

.BuyerAccountDashboard .stats-error {
  margin-bottom: var(--heading6);
}

.BuyerAccountDashboard .section-title {
  font-size: var(--heading6);
  color: var(--secondary-color);
  font-weight: 600;
  margin: 0;
}

.BuyerAccountDashboard .view-all {
  color: var(--btn-color);
  font-size: var(--smallfont);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.BuyerAccountDashboard .view-all:hover {
  text-decoration: underline;
}

/* Table Styles */
.BuyerAccountDashboard .table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  overflow-x: auto;
}

.BuyerAccountDashboard .table th,
.BuyerAccountDashboard .table td {
  padding: 12px 10px;
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
}

.BuyerAccountDashboard .table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--extrasmallfont);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.BuyerAccountDashboard .table tr:last-child td {
  border-bottom: none;
}

/* Remove old grid-based table-row/table-header/table-cell styles */

.BuyerAccountDashboard .content-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.BuyerAccountDashboard .content-image {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerAccountDashboard .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerAccountDashboard .content-info {
  display: flex;
  flex-direction: column;
}

.BuyerAccountDashboard .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerAccountDashboard .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerAccountDashboard .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerAccountDashboard .status-badge.downloaded {
  background-color: #d1fae5;
  color: #065f46;
}

.BuyerAccountDashboard .status-badge.pending {
  background-color: #f39c12;
  color: var(--white);
}
.BuyerAccountDashboard .status-badge.accepted {
  background-color: #3498db;
    color: var(--white);
}
.BuyerAccountDashboard .status-badge.approved {
  background-color: #3498db;
  color: var(--white);
}

.BuyerAccountDashboard .status-badge.completed {
  background-color: #2ecc71;
  color: var(--white);
}

.BuyerAccountDashboard .status-badge.active {
  background-color: #3498db;
  color: var(--white);
}

.BuyerAccountDashboard .status-badge.won {
  background-color: #d1fae5;
  color: #065f46;
}

.BuyerAccountDashboard .status-badge.lost {
  background-color: #f8d7da;
  color: #721c24;
}
.BuyerAccountDashboard .status-badge.outbid {
  background-color: #fff3cd;
  color: #856404;
}
.BuyerAccountDashboard .status-badge.expired {
  background-color: #f8d7da;
  color: #721c24;
}
.BuyerAccountDashboard .status-badge.cancelled {
  background-color: #e2e3e5;
  color: #383d41;
}
.BuyerAccountDashboard .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: black;
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: var(--basefont);
}
.BuyerAccountDashboard .view-btn {
  color: black;
  background-color: transparent;
  border: none;
  font-size: var(--heading6);
  cursor: pointer;
}
.BuyerAccountDashboard .action-btn:hover,
.BuyerAccountDashboard .view-btn:hover {
  transform: scale(1.02);
}

/* Responsive styles */
@media (max-width: 992px) {
  .BuyerAccountDashboard .table-header,
  .BuyerAccountDashboard .table-row {
    grid-template-columns: 0.5fr 1fr 2fr 1.5fr 1fr 1fr 0.5fr;
  }

  .BuyerAccountDashboard .content-title {
    max-width: 150px;
  }
}

@media (max-width: 1024px) {
  .BuyerAccountDashboard .stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .BuyerAccountDashboard .stats {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }

  .BuyerAccountDashboard .table {
    overflow-x: auto;
  }

  .BuyerAccountDashboard .table-header,
  .BuyerAccountDashboard .table-row {
    min-width: 700px;
  }
}
