/* Offer Acceptance Modal Styles */

/* Modal Overlay - Makes the modal appear as a popup */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: overlayFadeIn 0.3s ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.offer-acceptance-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 650px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.offer-acceptance-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.offer-acceptance-modal .modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.offer-acceptance-modal .modal-icon {
  color: #059669;
  font-size: 1.25rem;
}

.offer-acceptance-modal .modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.offer-acceptance-modal .modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.offer-acceptance-modal .modal-content {
  padding: 24px;
}

/* Offer Details Section */
.offer-acceptance-modal .offer-details-section {
  margin-bottom: 24px;
}

.offer-acceptance-modal .offer-details-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.offer-acceptance-modal .offer-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.offer-acceptance-modal .offer-info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.offer-acceptance-modal .offer-info-item .label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
}

.offer-acceptance-modal .offer-info-item .value {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.offer-acceptance-modal .offer-amount {
  color: #059669 !important;
  font-size: 1.25rem !important;
}

.offer-acceptance-modal .buyer-message-section {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #059669;
}

.offer-acceptance-modal .buyer-message-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.offer-acceptance-modal .buyer-message {
  font-size: 0.95rem;
  color: #374151;
  font-style: italic;
  line-height: 1.5;
}

/* Content Details Section */
.offer-acceptance-modal .content-details-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.offer-acceptance-modal .content-details-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.offer-acceptance-modal .content-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.offer-acceptance-modal .content-meta {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 8px;
}

.offer-acceptance-modal .listed-price {
  font-size: 0.875rem;
  color: #059669;
  font-weight: 500;
}

/* Notice Section */
.offer-acceptance-modal .notice-section {
  margin-bottom: 24px;
}

.offer-acceptance-modal .notice-box {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.offer-acceptance-modal .notice-box.acceptance {
  background: #ecfdf5;
  border: 1px solid #059669;
}

.offer-acceptance-modal .notice-box.rejection {
  background: #fef2f2;
  border: 1px solid #dc2626;
}

.offer-acceptance-modal .notice-box:last-child {
  margin-bottom: 0;
}

.offer-acceptance-modal .notice-icon {
  font-size: 1.25rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.offer-acceptance-modal .notice-box.acceptance .notice-icon {
  color: #059669;
}

.offer-acceptance-modal .notice-box.rejection .notice-icon {
  color: #dc2626;
}

.offer-acceptance-modal .notice-content h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.offer-acceptance-modal .notice-box.acceptance .notice-content h4 {
  color: #047857;
}

.offer-acceptance-modal .notice-box.rejection .notice-content h4 {
  color: #b91c1c;
}

.offer-acceptance-modal .notice-content ul {
  margin: 0;
  padding-left: 16px;
}

.offer-acceptance-modal .notice-box.acceptance .notice-content ul {
  color: #047857;
}

.offer-acceptance-modal .notice-box.rejection .notice-content ul {
  color: #b91c1c;
}

.offer-acceptance-modal .notice-content li {
  font-size: 0.875rem;
  margin-bottom: 4px;
}

.offer-acceptance-modal .notice-content strong {
  font-weight: 600;
}

/* Response Section */
.offer-acceptance-modal .response-section {
  margin-bottom: 24px;
}

.offer-acceptance-modal .response-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.offer-acceptance-modal .required {
  color: #dc2626;
  font-weight: 400;
}

.offer-acceptance-modal .response-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.95rem;
  color: #374151;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
}

.offer-acceptance-modal .response-textarea:focus {
  outline: none;
  border-color: #059669;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.offer-acceptance-modal .character-count {
  font-size: 0.75rem;
  color: #6b7280;
  text-align: right;
  margin-top: 4px;
}

/* Earnings Section */
.offer-acceptance-modal .earnings-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fefce8;
  border-radius: 8px;
  border: 1px solid #facc15;
}

.offer-acceptance-modal .earnings-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.offer-acceptance-modal .earnings-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.offer-acceptance-modal .earnings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.offer-acceptance-modal .earnings-item.total {
  padding-top: 12px;
  border-top: 1px solid #facc15;
  font-weight: 600;
}

.offer-acceptance-modal .earnings-item .label {
  font-size: 0.95rem;
  color: #374151;
}

.offer-acceptance-modal .earnings-item .value {
  font-size: 1rem;
  font-weight: 500;
  color: #1f2937;
}

.offer-acceptance-modal .earnings-item.deduction .value {
  color: #dc2626;
  font-weight: 500;
}

.offer-acceptance-modal .earnings-item.subtotal {
  padding-top: 8px;
  border-top: 1px solid #e5e7eb;
  margin-top: 8px;
}

.offer-acceptance-modal .earnings-item.subtotal .value {
  color: #374151;
  font-weight: 500;
}

.offer-acceptance-modal .earnings-item.total {
  padding-top: 12px;
  border-top: 2px solid #facc15;
  margin-top: 12px;
  font-weight: 600;
}

.offer-acceptance-modal .earnings-item.total .value {
  color: #059669;
  font-size: 1.125rem;
  font-weight: 600;
}

.offer-acceptance-modal .earnings-summary {
  margin-top: 16px;
  padding: 12px;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.offer-acceptance-modal .summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.offer-acceptance-modal .summary-item:last-child {
  margin-bottom: 0;
}

.offer-acceptance-modal .summary-item .buyer-amount {
  color: #2563eb;
  font-weight: 600;
}

.offer-acceptance-modal .summary-item .fees-amount {
  color: #dc2626;
  font-weight: 500;
}

/* Modal Actions */
.offer-acceptance-modal .modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 0 0 12px 12px;
}

.offer-acceptance-modal .modal-actions button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.offer-acceptance-modal .btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.offer-acceptance-modal .btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

.offer-acceptance-modal .btn-danger {
  background: #dc2626;
  color: white;
}

.offer-acceptance-modal .btn-danger:hover:not(:disabled) {
  background: #b91c1c;
}

.offer-acceptance-modal .btn-success {
  background: #059669;
  color: white;
}

.offer-acceptance-modal .btn-success:hover:not(:disabled) {
  background: #047857;
}

.offer-acceptance-modal .modal-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 640px) {
  .offer-acceptance-modal {
    width: 95%;
    margin: 10px;
  }

  .offer-acceptance-modal .offer-info-grid {
    grid-template-columns: 1fr;
  }

  .offer-acceptance-modal .modal-actions {
    flex-direction: column;
  }

  .offer-acceptance-modal .modal-actions button {
    width: 100%;
    justify-content: center;
  }
}

.offer-acceptance-modal .loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
}

.offer-acceptance-modal .spinner {
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
