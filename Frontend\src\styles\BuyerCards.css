.BuyerCards {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.BuyerCards .buyercardsbordercontainer {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading5);
}
.BuyerCards .BuyerCards__header {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  padding-bottom: var(--heading6);
}

.BuyerCards .BuyerCards__subtitle {
  font-size: var(--heading6);
  color: var(--secondary-color);
  font-weight: 500;
  margin: 0;
}

.BuyerCards .BuyerCards__add-btn {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  background-color: transparent;
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
  padding: var(--extrasmallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: var(--smallfont);
}

.BuyerCards .BuyerCards__add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.BuyerCards .BuyerCards__cards-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--basefont);
}

.BuyerCards .BuyerCards__card-item {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  margin-bottom: var(--smallfont);
  transition: box-shadow 0.3s ease;
}

.BuyerCards .BuyerCards__card-item:hover {
  box-shadow: var(--box-shadow-light);
}

.BuyerCards .BuyerCards__card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.BuyerCards .BuyerCards__card-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.BuyerCards .BuyerCards__card-logo {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.BuyerCards .BuyerCards__card-logo img {
  height: 24px;
  width: auto;
}

.BuyerCards .BuyerCards__card-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.BuyerCards .BuyerCards__card-number {
  font-size: var(--basefont);
  color: var(--text-color);
  letter-spacing: 1px;
  font-weight: 500;
}

.BuyerCards .BuyerCards__card-name {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 400;
}

.BuyerCards .BuyerCards__card-expiry {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  font-weight: 400;
}

.BuyerCards .BuyerCards__card-actions {
  display: flex;
  align-items: center;
  gap: 5px;
}

.BuyerCards .BuyerCards__default-btn {
  background: none;
  border: none;
  color: #ffc107;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 16px;
}

.BuyerCards .BuyerCards__default-btn:hover:not(:disabled) {
  background-color: rgba(255, 193, 7, 0.1);
  transform: scale(1.1);
}

.BuyerCards .BuyerCards__default-btn.active {
  color: #ffc107;
}

.BuyerCards .BuyerCards__default-btn:disabled {
  cursor: default;
}

.BuyerCards .BuyerCards__delete-btn {
  background: none;
  border: none;
  color: black;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
  font-size: var(--basefont);
}

.BuyerCards .BuyerCards__empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 150px;
  
  border-radius: var(--border-radius);
  
  color: var(--dark-gray);
  font-size: var(--basefont);
  text-align: center;
  gap: 16px;
}

.BuyerCards .BuyerCards__empty-icon {
  font-size: 48px;
  color: var(--light-gray);
  margin-bottom: 8px;
}

.BuyerCards .BuyerCards__add-first-btn {
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: var(--basefont);
}

.BuyerCards .BuyerCards__add-first-btn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.BuyerCards .BuyerCards__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: var(--basefont);
  color: var(--dark-gray);
}

.BuyerCards .BuyerCards__form-actions {
  display: flex;
  justify-content: center;
  margin-top: 24px;

  gap: 1rem;
}

.BuyerCards .BuyerCards__cancel-btn {
  background-color: transparent;
  color: var(--dark-gray);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: var(--basefont);
  padding: 14px 28px;
}

.BuyerCards .BuyerCards__cancel-btn:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
  border-color: var(--dark-gray) !important;
}

.BuyerCards .BuyerCards__form {
  width: 100%;
}

.BuyerCards .BuyerCards__form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;
}

.BuyerCards .BuyerCards__input-field {
  flex: 1;
  margin-bottom: 16px;
}

.BuyerCards .BuyerCards__input-field--full {
  width: 100%;
}

.BuyerCards .BuyerCards__input-field--half {
  flex: 1;
}

.BuyerCards .BuyerCards__input-field--card-number {
  flex: 1;
  position: relative;
}

.BuyerCards .BuyerCards__input-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
}

.BuyerCards .BuyerCards__input-container:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.BuyerCards .BuyerCards__input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  color: var(--dark-gray);
}

.BuyerCards .BuyerCards__input {
  width: 100%;
  padding: 12px 16px;
  border: none;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: transparent;
  outline: none;
}

.BuyerCards .BuyerCards__input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.BuyerCards .BuyerCards__stripe-element {
  width: 100%;
  border: none;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: transparent;
  outline: none;
  padding: 12px 16px;
  min-height: "20px";
}

.BuyerCards .BuyerCards__submit-btn {
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: var(--basefont);
}

.BuyerCards .BuyerCards__submit-btn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: scale(1.02);
}

/* Responsive styles */
@media (max-width: 768px) {
  .BuyerCards .BuyerCards__header {
    grid-template-columns: 1fr auto;
    gap: var(--basefont);
  }

  .BuyerCards .BuyerCards__add-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 350px) {
  .BuyerCards .BuyerCards__card-info,
  .BuyerCards .BuyerCards__card-actions {
    display: grid;
  }
  .BuyerCards .BuyerCards__stripe-element {
    padding: 12px 10px;
  }
  .BuyerCards .BuyerCards__header {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }
  .BuyerCards .BuyerCards__form-actions {
    flex-direction: column;
  }
}
